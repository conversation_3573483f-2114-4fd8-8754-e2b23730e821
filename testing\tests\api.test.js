const APIClient = require('../utils/api-client');
const TestDataGenerator = require('../utils/test-data');

describe('ATMA API Tests', () => {
  let apiClient;
  let dataGenerator;
  let testUser;
  let authToken;

  beforeAll(() => {
    apiClient = new APIClient();
    dataGenerator = new TestDataGenerator();
  });

  beforeEach(() => {
    testUser = dataGenerator.generateUserData();
  });

  afterEach(async () => {
    // Cleanup: try to delete test account
    if (authToken) {
      try {
        apiClient.setAuthToken(authToken);
        await apiClient.deleteAccount();
      } catch (error) {
        // Ignore cleanup errors
      }
      authToken = null;
    }
  });

  describe('Authentication', () => {
    test('should register a new user', async () => {
      const response = await apiClient.register(testUser);
      
      expect(response.success).toBe(true);
      expect(response.data.user).toBeDefined();
      expect(response.data.user.email).toBe(testUser.email);
      expect(response.data.token).toBeDefined();
      
      authToken = response.data.token;
    });

    test('should login with valid credentials', async () => {
      // First register
      const registerResponse = await apiClient.register(testUser);
      authToken = registerResponse.data.token;
      
      // Then login
      const loginResponse = await apiClient.login({
        email: testUser.email,
        password: testUser.password
      });
      
      expect(loginResponse.success).toBe(true);
      expect(loginResponse.data.user).toBeDefined();
      expect(loginResponse.data.token).toBeDefined();
      
      authToken = loginResponse.data.token;
    });

    test('should reject invalid login credentials', async () => {
      await expect(apiClient.login({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })).rejects.toThrow();
    });

    test('should get user profile when authenticated', async () => {
      // Register and login
      const registerResponse = await apiClient.register(testUser);
      authToken = registerResponse.data.token;
      apiClient.setAuthToken(authToken);
      
      const profileResponse = await apiClient.getProfile();
      
      expect(profileResponse.success).toBe(true);
      expect(profileResponse.data.email).toBe(testUser.email);
    });

    test('should reject profile access without authentication', async () => {
      await expect(apiClient.getProfile()).rejects.toThrow();
    });
  });

  describe('Profile Management', () => {
    beforeEach(async () => {
      const registerResponse = await apiClient.register(testUser);
      authToken = registerResponse.data.token;
      apiClient.setAuthToken(authToken);
    });

    test('should update user profile', async () => {
      const profileUpdate = dataGenerator.generateProfileUpdateData();
      const response = await apiClient.updateProfile(profileUpdate);

      expect(response.success).toBe(true);
      expect(response.data.user.username).toBe(profileUpdate.username);
      expect(response.data.user.email).toBe(profileUpdate.email);
    });

    test('should change password', async () => {
      const passwordData = dataGenerator.generatePasswordChangeData();
      const response = await apiClient.changePassword(passwordData);

      expect(response.success).toBe(true);
      expect(response.message).toContain('Password changed successfully');
    });

    test('should get token balance', async () => {
      const response = await apiClient.getTokenBalance();

      expect(response.success).toBe(true);
      expect(response.data.user_id).toBeDefined();
      expect(typeof response.data.token_balance).toBe('number');
    });
  });

  describe('Assessment', () => {
    beforeEach(async () => {
      const registerResponse = await apiClient.register(testUser);
      authToken = registerResponse.data.token;
      apiClient.setAuthToken(authToken);
    });

    test('should submit assessment', async () => {
      const assessmentData = dataGenerator.generateAssessmentData();
      const response = await apiClient.submitAssessment(assessmentData);
      
      expect(response.success).toBe(true);
      expect(response.data.jobId).toBeDefined();
      expect(response.data.status).toBe('queued');
    });

    test('should get assessment status', async () => {
      const assessmentData = dataGenerator.generateAssessmentData();
      const submitResponse = await apiClient.submitAssessment(assessmentData);
      const jobId = submitResponse.data.jobId;
      
      const statusResponse = await apiClient.getAssessmentStatus(jobId);
      
      expect(statusResponse.success).toBe(true);
      expect(statusResponse.data.jobId).toBe(jobId);
      expect(['queued', 'processing', 'completed', 'failed']).toContain(statusResponse.data.status);
    });

    test('should reject invalid assessment data', async () => {
      await expect(apiClient.submitAssessment({ invalid: 'data' })).rejects.toThrow();
    });
  });

  describe('Schools Management', () => {
    beforeEach(async () => {
      const registerResponse = await apiClient.register(testUser);
      authToken = registerResponse.data.token;
      apiClient.setAuthToken(authToken);
    });

    test('should get schools list', async () => {
      const response = await apiClient.getSchools();

      expect(response.success).toBe(true);
      expect(response.data.schools).toBeDefined();
      expect(Array.isArray(response.data.schools)).toBe(true);
      expect(response.data.pagination).toBeDefined();
    });

    test('should create new school', async () => {
      const schoolData = dataGenerator.generateSchoolData();
      const response = await apiClient.createSchool(schoolData);

      expect(response.success).toBe(true);
      expect(response.data.school).toBeDefined();
      expect(response.data.school.name).toBe(schoolData.name);
    });

    test('should search schools', async () => {
      const response = await apiClient.getSchools({ search: 'SMA', limit: 5 });

      expect(response.success).toBe(true);
      expect(response.data.schools).toBeDefined();
      expect(response.data.pagination.limit).toBe(5);
    });
  });

  describe('Archive', () => {
    beforeEach(async () => {
      const registerResponse = await apiClient.register(testUser);
      authToken = registerResponse.data.token;
      apiClient.setAuthToken(authToken);
    });

    test('should get user results', async () => {
      const response = await apiClient.getResults();

      expect(response.success).toBe(true);
      expect(response.data.results).toBeDefined();
      expect(Array.isArray(response.data.results)).toBe(true);
    });

    test('should get user jobs', async () => {
      const response = await apiClient.getJobs();

      expect(response.success).toBe(true);
      expect(response.data.jobs).toBeDefined();
      expect(Array.isArray(response.data.jobs)).toBe(true);
    });

    test('should get job statistics', async () => {
      const response = await apiClient.getJobStats();

      expect(response.success).toBe(true);
      expect(response.data.total_jobs).toBeDefined();
      expect(typeof response.data.total_jobs).toBe('number');
    });

    test('should get user statistics', async () => {
      const response = await apiClient.getStats({ type: 'user', scope: 'overview' });

      expect(response.success).toBe(true);
      expect(response.data.total_assessments).toBeDefined();
      expect(typeof response.data.total_assessments).toBe('number');
    });
  });

  describe('Chatbot', () => {
    beforeEach(async () => {
      const registerResponse = await apiClient.register(testUser);
      authToken = registerResponse.data.token;
      apiClient.setAuthToken(authToken);
    });

    test('should create conversation', async () => {
      const conversationData = dataGenerator.generateChatbotConversationData();
      const response = await apiClient.createConversation(conversationData);
      
      expect(response.success).toBe(true);
      expect(response.data.id).toBeDefined();
      expect(response.data.title).toBe(conversationData.title);
    });

    test('should send message and get response', async () => {
      // Create conversation first
      const conversationData = dataGenerator.generateChatbotConversationData();
      const conversationResponse = await apiClient.createConversation(conversationData);
      const conversationId = conversationResponse.data.id;
      
      // Send message
      const messageData = dataGenerator.generateChatMessage();
      const messageResponse = await apiClient.sendMessage(conversationId, messageData);
      
      expect(messageResponse.success).toBe(true);
      expect(messageResponse.data.user_message).toBeDefined();
      expect(messageResponse.data.assistant_message).toBeDefined();
    });

    test('should get conversations', async () => {
      const response = await apiClient.getConversations();

      expect(response.success).toBe(true);
      expect(response.data.conversations).toBeDefined();
      expect(Array.isArray(response.data.conversations)).toBe(true);
    });

    test('should get conversation by id', async () => {
      // Create conversation first
      const conversationData = dataGenerator.generateChatbotConversationData();
      const conversationResponse = await apiClient.createConversation(conversationData);
      const conversationId = conversationResponse.data.id;

      const response = await apiClient.getConversationById(conversationId, { include_messages: true });

      expect(response.success).toBe(true);
      expect(response.data.id).toBe(conversationId);
      expect(response.data.title).toBe(conversationData.title);
    });

    test('should update conversation', async () => {
      // Create conversation first
      const conversationData = dataGenerator.generateChatbotConversationData();
      const conversationResponse = await apiClient.createConversation(conversationData);
      const conversationId = conversationResponse.data.id;

      const updateData = { title: "Updated Chat Title", status: "archived" };
      const response = await apiClient.updateConversation(conversationId, updateData);

      expect(response.success).toBe(true);
      expect(response.data.title).toBe(updateData.title);
      expect(response.data.status).toBe(updateData.status);
    });

    test('should get conversation messages', async () => {
      // Create conversation and send message first
      const conversationData = dataGenerator.generateChatbotConversationData();
      const conversationResponse = await apiClient.createConversation(conversationData);
      const conversationId = conversationResponse.data.id;

      const messageData = dataGenerator.generateChatMessage();
      await apiClient.sendMessage(conversationId, messageData);

      const response = await apiClient.getMessages(conversationId);

      expect(response.success).toBe(true);
      expect(response.data.messages).toBeDefined();
      expect(Array.isArray(response.data.messages)).toBe(true);
    });

    test('should auto-initialize chatbot', async () => {
      const response = await apiClient.autoInitializeChatbot();

      expect(response.success).toBe(true);
      expect(response.data.conversation).toBeDefined();
      expect(response.data.welcome_message).toBeDefined();
    });
  });

  describe('Notifications', () => {
    test('should get notification service health', async () => {
      const response = await apiClient.getNotificationHealth();

      expect(response.success).toBe(true);
      expect(response.status).toBe('healthy');
      expect(response.service).toBe('notification-service');
      expect(response.websocket).toBeDefined();
    });
  });

  describe('Admin Functions', () => {
    let adminToken;

    test('should login as admin', async () => {
      const adminCredentials = {
        username: 'admin',
        password: 'adminPassword1'
      };

      try {
        const response = await apiClient.adminLogin(adminCredentials);

        expect(response.success).toBe(true);
        expect(response.data.admin).toBeDefined();
        expect(response.data.token).toBeDefined();

        adminToken = response.data.token;
      } catch (error) {
        // Admin might not exist in test environment, skip this test
        console.log('Admin login test skipped - admin not configured');
      }
    });

    test('should register new admin', async () => {
      if (!adminToken) {
        console.log('Admin register test skipped - no admin token');
        return;
      }

      apiClient.setAuthToken(adminToken);
      const adminData = dataGenerator.generateAdminData();

      try {
        const response = await apiClient.adminRegister(adminData);

        expect(response.success).toBe(true);
        expect(response.data.admin).toBeDefined();
        expect(response.data.admin.username).toBe(adminData.username);
      } catch (error) {
        // Might not have permission, skip
        console.log('Admin register test skipped - insufficient permissions');
      }
    });
  });

  describe('Response Format Validation', () => {
    beforeEach(async () => {
      const registerResponse = await apiClient.register(testUser);
      authToken = registerResponse.data.token;
      apiClient.setAuthToken(authToken);
    });

    test('should validate success response format', async () => {
      const response = await apiClient.getProfile();

      expect(() => apiClient.validateSuccessResponse(response)).not.toThrow();
      expect(response.success).toBe(true);
      expect(response.data).toBeDefined();
    });

    test('should validate pagination response format', async () => {
      const response = await apiClient.getResults();

      expect(() => apiClient.validatePaginationResponse(response)).not.toThrow();
      expect(response.data.pagination).toBeDefined();
      expect(response.data.pagination.page).toBeDefined();
      expect(response.data.pagination.limit).toBeDefined();
    });

    test('should validate error response format', async () => {
      try {
        await apiClient.login({ email: '<EMAIL>', password: 'wrong' });
      } catch (error) {
        const errorResponse = error.response?.data;
        if (errorResponse) {
          expect(() => apiClient.validateErrorResponse(errorResponse)).not.toThrow();
          expect(errorResponse.success).toBe(false);
          expect(errorResponse.error).toBeDefined();
          expect(errorResponse.error.code).toBeDefined();
          expect(errorResponse.error.message).toBeDefined();
        }
      }
    });
  });

  describe('Health Check', () => {
    test('should return healthy status', async () => {
      const response = await apiClient.healthCheck();

      expect(response.success).toBe(true);
      expect(response.status).toBe('healthy');
    });
  });
});
