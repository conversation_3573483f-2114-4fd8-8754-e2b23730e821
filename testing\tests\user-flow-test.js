#!/usr/bin/env node

const APIClient = require('../utils/api-client');
const TestDataGenerator = require('../utils/test-data');
const TestLogger = require('../utils/test-logger');
require('dotenv').config();

class UserFlowTest {
  constructor() {
    this.logger = new TestLogger('User Flow Test');
    this.dataGenerator = new TestDataGenerator();
  }

  async runTest() {
    this.logger.header('User Flow Test - Registration to Assessment');
    
    const apiClient = new APIClient();
    let userData = null;
    let token = null;

    try {
      // Step 1: Test user registration
      this.logger.step('Test user registration');
      const testUser = this.dataGenerator.generateUserData();
      const registerResponse = await apiClient.register(testUser);
      
      if (!registerResponse.success) {
        throw new Error('Registration failed');
      }
      
      userData = registerResponse.data.user;
      token = registerResponse.data.token;
      this.logger.success(`User registered: ${userData.email}`);
      this.logger.json('User Data', userData);

      // Step 2: Test user login
      this.logger.step('Test user login');
      const loginResponse = await apiClient.login({
        email: testUser.email,
        password: testUser.password
      });
      
      if (!loginResponse.success) {
        throw new Error('Login failed');
      }
      
      token = loginResponse.data.token;
      apiClient.setAuthToken(token);
      this.logger.success('User logged in successfully');

      // Step 3: Test get profile
      this.logger.step('Test get user profile');
      const profileResponse = await apiClient.getProfile();
      
      if (!profileResponse.success) {
        throw new Error('Get profile failed');
      }
      
      this.logger.success('Profile retrieved successfully');
      this.logger.json('Profile Data', profileResponse.data);

      // Step 4: Test update profile
      this.logger.step('Test update user profile');
      const profileUpdate = this.dataGenerator.generateProfileUpdateData();
      const updateResponse = await apiClient.updateProfile(profileUpdate);
      
      if (!updateResponse.success) {
        throw new Error('Profile update failed');
      }
      
      this.logger.success('Profile updated successfully');
      this.logger.json('Updated Profile', updateResponse.data.user);

      // Step 5: Test assessment submission
      this.logger.step('Test assessment submission');
      const assessmentData = this.dataGenerator.generateAssessmentData();
      const assessmentResponse = await apiClient.submitAssessment(assessmentData);
      
      if (!assessmentResponse.success) {
        throw new Error('Assessment submission failed');
      }
      
      const jobId = assessmentResponse.data.jobId;
      this.logger.success(`Assessment submitted with job ID: ${jobId}`);
      this.logger.json('Assessment Response', assessmentResponse.data);

      // Step 6: Test assessment status checking
      this.logger.step('Test assessment status checking');
      let attempts = 0;
      const maxAttempts = 30; // 5 minutes with 10-second intervals
      let resultId = null;
      
      while (attempts < maxAttempts) {
        const statusResponse = await apiClient.getAssessmentStatus(jobId);
        
        if (!statusResponse.success) {
          throw new Error('Failed to get assessment status');
        }
        
        this.logger.info(`Assessment status: ${statusResponse.data.status} (attempt ${attempts + 1})`);
        
        if (statusResponse.data.status === 'completed') {
          resultId = statusResponse.data.resultId;
          this.logger.success('Assessment completed successfully');
          break;
        } else if (statusResponse.data.status === 'failed') {
          throw new Error('Assessment processing failed');
        }
        
        attempts++;
        if (attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
        }
      }
      
      if (!resultId) {
        throw new Error('Assessment did not complete within timeout');
      }

      // Step 7: Test get assessment results
      this.logger.step('Test get assessment results');
      const resultResponse = await apiClient.getResultById(resultId);
      
      if (!resultResponse.success) {
        throw new Error('Failed to retrieve assessment results');
      }
      
      this.logger.success('Assessment results retrieved successfully');
      this.logger.json('Assessment Results', resultResponse.data.persona_profile);

      // Step 8: Test get all results
      this.logger.step('Test get all user results');
      const allResultsResponse = await apiClient.getResults();
      
      if (!allResultsResponse.success) {
        throw new Error('Failed to retrieve all results');
      }
      
      this.logger.success(`Retrieved ${allResultsResponse.data.results.length} results`);

      // Step 9: Test chatbot functionality
      this.logger.step('Test chatbot functionality');
      
      // Create conversation
      const conversationData = this.dataGenerator.generateChatbotConversationData();
      const conversationResponse = await apiClient.createConversation(conversationData);
      
      if (!conversationResponse.success) {
        throw new Error('Failed to create conversation');
      }
      
      const conversationId = conversationResponse.data.id;
      this.logger.success(`Conversation created: ${conversationId}`);
      
      // Send message
      const messageData = this.dataGenerator.generateChatMessage();
      const messageResponse = await apiClient.sendMessage(conversationId, messageData);
      
      if (!messageResponse.success) {
        throw new Error('Failed to send message');
      }
      
      this.logger.success('Message sent and response received');
      this.logger.json('Chatbot Response', messageResponse.data.assistant_message);

      // Step 10: Test get conversations
      this.logger.step('Test get conversations');
      const conversationsResponse = await apiClient.getConversations();
      
      if (!conversationsResponse.success) {
        throw new Error('Failed to get conversations');
      }
      
      this.logger.success(`Retrieved ${conversationsResponse.data.conversations.length} conversations`);

      // Step 11: Test logout
      this.logger.step('Test user logout');
      const logoutResponse = await apiClient.logout();
      
      if (!logoutResponse.success) {
        throw new Error('Logout failed');
      }
      
      this.logger.success('User logged out successfully');

      // Step 12: Cleanup (optional)
      if (process.env.CLEANUP_AFTER_TEST === 'true') {
        this.logger.step('Cleanup - Delete account');
        
        // Re-login for cleanup
        const cleanupLoginResponse = await apiClient.login({
          email: testUser.email,
          password: testUser.password
        });
        
        if (cleanupLoginResponse.success) {
          apiClient.setAuthToken(cleanupLoginResponse.data.token);
          
          try {
            await apiClient.deleteAccount();
            this.logger.success('Test account deleted successfully');
          } catch (cleanupError) {
            this.logger.warning('Account cleanup failed (endpoint may not be implemented)');
          }
        }
      }

      this.logger.footer();
      this.logger.success('User flow test completed successfully');

      return {
        userId: userData.id,
        email: userData.email,
        jobId,
        resultId,
        conversationId
      };

    } catch (error) {
      this.logger.error('User flow test failed', error);
      throw error;
    }
  }

  async testErrorHandling() {
    this.logger.header('Error Handling Test');
    
    const apiClient = new APIClient();
    
    try {
      // Test invalid login
      this.logger.step('Test invalid login');
      try {
        await apiClient.login({
          email: '<EMAIL>',
          password: 'wrongpassword'
        });
        this.logger.error('Login should have failed');
      } catch (loginError) {
        this.logger.success('Invalid login correctly rejected');
      }

      // Test unauthorized access
      this.logger.step('Test unauthorized access');
      try {
        await apiClient.getProfile();
        this.logger.error('Unauthorized access should have failed');
      } catch (authError) {
        this.logger.success('Unauthorized access correctly rejected');
      }

      // Test invalid assessment data
      this.logger.step('Test invalid assessment data');
      const testUser = this.dataGenerator.generateUserData();
      const registerResponse = await apiClient.register(testUser);
      apiClient.setAuthToken(registerResponse.data.token);
      
      try {
        await apiClient.submitAssessment({ invalid: 'data' });
        this.logger.error('Invalid assessment should have failed');
      } catch (assessmentError) {
        this.logger.success('Invalid assessment correctly rejected');
      }

      this.logger.success('Error handling test completed');

    } catch (error) {
      this.logger.error('Error handling test failed', error);
      throw error;
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  const test = new UserFlowTest();
  
  test.runTest()
    .then(() => {
      console.log('\n');
      return test.testErrorHandling();
    })
    .then(() => {
      console.log('\n✅ All user flow tests completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ User flow tests failed:', error.message);
      process.exit(1);
    });
}

module.exports = UserFlowTest;
