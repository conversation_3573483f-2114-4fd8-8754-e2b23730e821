const APIClient = require('../utils/api-client');
const WebSocketClient = require('../utils/websocket-client');
const TestDataGenerator = require('../utils/test-data');

describe('WebSocket Tests', () => {
  let apiClient;
  let wsClient;
  let dataGenerator;
  let testUser;
  let authToken;

  beforeAll(() => {
    apiClient = new APIClient();
    dataGenerator = new TestDataGenerator();
  });

  beforeEach(async () => {
    testUser = dataGenerator.generateUserData();
    wsClient = new WebSocketClient();
    
    // Register and login user
    const registerResponse = await apiClient.register(testUser);
    authToken = registerResponse.data.token;
    apiClient.setAuthToken(authToken);
  });

  afterEach(async () => {
    // Cleanup WebSocket
    if (wsClient && wsClient.isConnected()) {
      wsClient.disconnect();
    }
    
    // Cleanup user account
    if (authToken) {
      try {
        await apiClient.deleteAccount();
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  });

  describe('Connection Management', () => {
    test('should connect to WebSocket', async () => {
      await wsClient.connect();
      
      expect(wsClient.isConnected()).toBe(true);
      expect(wsClient.isAuth()).toBe(false);
    });

    test('should authenticate WebSocket with valid token', async () => {
      await wsClient.connect();
      await wsClient.authenticate(authToken);
      
      expect(wsClient.isConnected()).toBe(true);
      expect(wsClient.isAuth()).toBe(true);
    });

    test('should reject authentication with invalid token', async () => {
      await wsClient.connect();
      
      await expect(wsClient.authenticate('invalid-token')).rejects.toThrow();
      expect(wsClient.isAuth()).toBe(false);
    });

    test('should handle disconnect and reconnect', async () => {
      await wsClient.connect();
      await wsClient.authenticate(authToken);
      
      expect(wsClient.isConnected()).toBe(true);
      expect(wsClient.isAuth()).toBe(true);
      
      wsClient.disconnect();
      expect(wsClient.isConnected()).toBe(false);
      expect(wsClient.isAuth()).toBe(false);
      
      await wsClient.connect();
      await wsClient.authenticate(authToken);
      
      expect(wsClient.isConnected()).toBe(true);
      expect(wsClient.isAuth()).toBe(true);
    });
  });

  describe('Notification Handling', () => {
    beforeEach(async () => {
      await wsClient.connect();
      await wsClient.authenticate(authToken);
    });

    test('should receive analysis-started notification', async () => {
      // Submit assessment to trigger notification
      const assessmentData = dataGenerator.generateAssessmentData();
      await apiClient.submitAssessment(assessmentData);
      
      // Wait for notification
      const notification = await wsClient.waitForNotification('analysis-started', 30000);
      
      expect(notification).toBeDefined();
      expect(notification.type).toBe('analysis-started');
      expect(notification.data.jobId).toBeDefined();
      expect(notification.data.status).toBe('started');
    });

    test('should receive analysis-complete notification', async () => {
      // Submit assessment to trigger notification
      const assessmentData = dataGenerator.generateAssessmentData();
      await apiClient.submitAssessment(assessmentData);
      
      // Wait for completion notification (this may take several minutes)
      const notification = await wsClient.waitForNotification('analysis-complete', 300000);
      
      expect(notification).toBeDefined();
      expect(notification.type).toBe('analysis-complete');
      expect(notification.data.jobId).toBeDefined();
      expect(notification.data.resultId).toBeDefined();
      expect(notification.data.status).toBe('completed');
    }, 300000); // 5 minute timeout

    test('should store and retrieve notifications', async () => {
      // Submit assessment to trigger notification
      const assessmentData = dataGenerator.generateAssessmentData();
      await apiClient.submitAssessment(assessmentData);
      
      // Wait for at least one notification
      await wsClient.waitForNotification('analysis-started', 30000);
      
      // Check stored notifications
      const allNotifications = wsClient.getNotifications();
      expect(allNotifications.length).toBeGreaterThan(0);
      
      const startedNotifications = wsClient.getNotifications('analysis-started');
      expect(startedNotifications.length).toBeGreaterThan(0);
      expect(startedNotifications[0].type).toBe('analysis-started');
    });

    test('should clear notifications', async () => {
      // Submit assessment to trigger notification
      const assessmentData = dataGenerator.generateAssessmentData();
      await apiClient.submitAssessment(assessmentData);
      
      // Wait for notification
      await wsClient.waitForNotification('analysis-started', 30000);
      
      // Verify notifications exist
      expect(wsClient.getNotifications().length).toBeGreaterThan(0);
      
      // Clear notifications
      wsClient.clearNotifications();
      expect(wsClient.getNotifications().length).toBe(0);
    });
  });

  describe('Error Handling', () => {
    test('should handle authentication timeout', async () => {
      await wsClient.connect();
      
      // Don't authenticate and wait for timeout
      await new Promise(resolve => setTimeout(resolve, 11000)); // Wait longer than auth timeout
      
      expect(wsClient.isAuth()).toBe(false);
    }, 15000);

    test('should handle connection errors', async () => {
      // Try to connect to invalid URL
      const invalidWsClient = new WebSocketClient();
      invalidWsClient.url = 'http://invalid-url:9999';
      
      await expect(invalidWsClient.connect()).rejects.toThrow();
    });

    test('should timeout when waiting for non-existent notification', async () => {
      await wsClient.connect();
      await wsClient.authenticate(authToken);
      
      // Wait for notification that will never come
      await expect(
        wsClient.waitForNotification('non-existent-notification', 2000)
      ).rejects.toThrow('Timeout waiting for notification');
    });
  });

  describe('Integration with Assessment Flow', () => {
    beforeEach(async () => {
      await wsClient.connect();
      await wsClient.authenticate(authToken);
    });

    test('should receive notifications for complete assessment flow', async () => {
      // Submit assessment
      const assessmentData = dataGenerator.generateAssessmentData();
      const submitResponse = await apiClient.submitAssessment(assessmentData);
      const jobId = submitResponse.data.jobId;

      // Wait for started notification
      const startedNotification = await wsClient.waitForNotification('analysis-started', 30000);
      expect(startedNotification.data.jobId).toBe(jobId);

      // Wait for completion notification
      const completeNotification = await wsClient.waitForNotification('analysis-complete', 300000);
      expect(completeNotification.data.jobId).toBe(jobId);
      expect(completeNotification.data.resultId).toBeDefined();

      // Verify we can get the result via API
      const resultResponse = await apiClient.getResultById(completeNotification.data.resultId);
      expect(resultResponse.success).toBe(true);
      expect(resultResponse.data.persona_profile).toBeDefined();
    }, 300000); // 5 minute timeout
  });

  describe('Advanced WebSocket Features', () => {
    beforeEach(async () => {
      await wsClient.connect();
      await wsClient.authenticate(authToken);
    });

    test('should handle multiple concurrent notifications', async () => {
      // Submit multiple assessments
      const assessmentData1 = dataGenerator.generateAssessmentData();
      const assessmentData2 = dataGenerator.generateAssessmentData();

      const [submit1, submit2] = await Promise.all([
        apiClient.submitAssessment(assessmentData1),
        apiClient.submitAssessment(assessmentData2)
      ]);

      // Wait for both started notifications
      const notifications = [];
      for (let i = 0; i < 2; i++) {
        const notification = await wsClient.waitForNotification('analysis-started', 30000);
        notifications.push(notification);
      }

      expect(notifications).toHaveLength(2);
      const jobIds = notifications.map(n => n.data.jobId);
      expect(jobIds).toContain(submit1.data.jobId);
      expect(jobIds).toContain(submit2.data.jobId);
    });

    test('should validate notification data structure', async () => {
      const assessmentData = dataGenerator.generateAssessmentData();
      await apiClient.submitAssessment(assessmentData);

      const notification = await wsClient.waitForNotification('analysis-started', 30000);

      // Validate notification structure according to spec
      expect(notification.data.jobId).toBeDefined();
      expect(notification.data.status).toBe('started');
      expect(notification.data.message).toBeDefined();
      expect(notification.data.metadata).toBeDefined();
      expect(notification.data.metadata.assessmentName).toBeDefined();
      expect(notification.data.metadata.estimatedProcessingTime).toBeDefined();
      expect(notification.data.timestamp).toBeDefined();
    });

    test('should handle WebSocket reconnection with re-authentication', async () => {
      // Verify initial connection
      expect(wsClient.isConnected()).toBe(true);
      expect(wsClient.isAuth()).toBe(true);

      // Force disconnect
      wsClient.disconnect();
      expect(wsClient.isConnected()).toBe(false);
      expect(wsClient.isAuth()).toBe(false);

      // Reconnect and re-authenticate
      await wsClient.connect();
      await wsClient.authenticate(authToken);

      expect(wsClient.isConnected()).toBe(true);
      expect(wsClient.isAuth()).toBe(true);

      // Verify notifications still work
      const assessmentData = dataGenerator.generateAssessmentData();
      await apiClient.submitAssessment(assessmentData);

      const notification = await wsClient.waitForNotification('analysis-started', 30000);
      expect(notification).toBeDefined();
    });

    test('should handle authentication with expired token', async () => {
      // Create an expired token (this is a mock - in real scenario you'd use an actually expired token)
      const expiredToken = 'expired.token.here';

      await wsClient.connect();

      await expect(wsClient.authenticate(expiredToken)).rejects.toThrow();
      expect(wsClient.isAuth()).toBe(false);
    });
  });

  describe('WebSocket URL Configuration', () => {
    test('should connect to API Gateway URL by default', () => {
      const defaultWsClient = new WebSocketClient();
      expect(defaultWsClient.url).toBe('http://localhost:3000');
    });

    test('should respect environment variable for WebSocket URL', () => {
      const originalUrl = process.env.WEBSOCKET_URL;
      process.env.WEBSOCKET_URL = 'http://custom-url:3000';

      const customWsClient = new WebSocketClient();
      expect(customWsClient.url).toBe('http://custom-url:3000');

      // Restore original
      if (originalUrl) {
        process.env.WEBSOCKET_URL = originalUrl;
      } else {
        delete process.env.WEBSOCKET_URL;
      }
    });
  });
});
