const io = require('socket.io-client');
const chalk = require('chalk');
require('dotenv').config();

class WebSocketClient {
  constructor() {
    this.url = process.env.WEBSOCKET_URL || 'http://localhost:3000';
    this.socket = null;
    this.isAuthenticated = false;
    this.notifications = [];
    this.verbose = process.env.VERBOSE_LOGGING === 'true';
    this.authTimeout = 10000; // 10 seconds as per documentation
  }

  connect() {
    return new Promise((resolve, reject) => {
      if (this.verbose) {
        console.log(chalk.blue(`🔌 Connecting to WebSocket: ${this.url}`));
      }

      this.socket = io(this.url, {
        autoConnect: false,
        transports: ['websocket', 'polling'],
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        timeout: 20000,
        forceNew: false
      });

      // Connection events
      this.socket.on('connect', () => {
        if (this.verbose) {
          console.log(chalk.green('✅ WebSocket connected'));
        }
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.error(chalk.red('❌ WebSocket connection error:', error.message));
        reject(error);
      });

      this.socket.on('disconnect', (reason) => {
        if (this.verbose) {
          console.log(chalk.yellow(`🔌 WebSocket disconnected: ${reason}`));
        }
        this.isAuthenticated = false;
      });

      // Authentication events
      this.socket.on('authenticated', (data) => {
        if (this.verbose) {
          console.log(chalk.green('🔐 WebSocket authenticated:', data));
        }
        this.isAuthenticated = true;
      });

      this.socket.on('auth_error', (data) => {
        console.error(chalk.red('🔐 WebSocket auth error:', data.message));
        this.isAuthenticated = false;
      });

      // Notification events
      this.socket.on('analysis-started', (data) => {
        if (this.verbose) {
          console.log(chalk.cyan('📊 Analysis started:', data));
        }
        try {
          this.validateNotificationData('analysis-started', data);
        } catch (error) {
          console.warn(chalk.yellow(`⚠️ Notification validation warning: ${error.message}`));
        }
        this.notifications.push({
          type: 'analysis-started',
          data,
          timestamp: new Date()
        });
      });

      this.socket.on('analysis-complete', (data) => {
        if (this.verbose) {
          console.log(chalk.green('✅ Analysis complete:', data));
        }
        try {
          this.validateNotificationData('analysis-complete', data);
        } catch (error) {
          console.warn(chalk.yellow(`⚠️ Notification validation warning: ${error.message}`));
        }
        this.notifications.push({
          type: 'analysis-complete',
          data,
          timestamp: new Date()
        });
      });

      this.socket.on('analysis-failed', (data) => {
        if (this.verbose) {
          console.log(chalk.red('❌ Analysis failed:', data));
        }
        try {
          this.validateNotificationData('analysis-failed', data);
        } catch (error) {
          console.warn(chalk.yellow(`⚠️ Notification validation warning: ${error.message}`));
        }
        this.notifications.push({
          type: 'analysis-failed',
          data,
          timestamp: new Date()
        });
      });

      this.socket.connect();
    });
  }

  authenticate(token) {
    return new Promise((resolve, reject) => {
      if (!this.socket || !this.socket.connected) {
        reject(new Error('WebSocket not connected'));
        return;
      }

      if (this.verbose) {
        console.log(chalk.blue('🔐 Authenticating WebSocket...'));
      }

      // Set timeout for authentication
      const authTimeoutId = setTimeout(() => {
        reject(new Error('Authentication timeout'));
      }, this.authTimeout);

      // Listen for authentication response
      const onAuthenticated = (data) => {
        clearTimeout(authTimeoutId);
        this.socket.off('authenticated', onAuthenticated);
        this.socket.off('auth_error', onAuthError);
        resolve(data);
      };

      const onAuthError = (data) => {
        clearTimeout(authTimeoutId);
        this.socket.off('authenticated', onAuthenticated);
        this.socket.off('auth_error', onAuthError);
        reject(new Error(data.message));
      };

      this.socket.on('authenticated', onAuthenticated);
      this.socket.on('auth_error', onAuthError);

      // Send authentication
      this.socket.emit('authenticate', { token });
    });
  }

  waitForNotification(type, timeout = 300000) { // 5 minutes default
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Timeout waiting for notification: ${type}`));
      }, timeout);

      const checkNotifications = () => {
        const notification = this.notifications.find(n => n.type === type);
        if (notification) {
          clearTimeout(timeoutId);
          resolve(notification);
        } else {
          setTimeout(checkNotifications, 1000); // Check every second
        }
      };

      checkNotifications();
    });
  }

  getNotifications(type = null) {
    if (type) {
      return this.notifications.filter(n => n.type === type);
    }
    return this.notifications;
  }

  clearNotifications() {
    this.notifications = [];
  }

  disconnect() {
    if (this.socket) {
      if (this.verbose) {
        console.log(chalk.yellow('🔌 Disconnecting WebSocket...'));
      }
      this.socket.disconnect();
      this.socket = null;
      this.isAuthenticated = false;
    }
  }

  isConnected() {
    return this.socket && this.socket.connected;
  }

  isAuth() {
    return this.isAuthenticated;
  }

  // Validate notification data structure according to WebSocket manual
  validateNotificationData(eventType, data) {
    if (!data || typeof data !== 'object') {
      throw new Error(`Invalid notification data for ${eventType}: not an object`);
    }

    // Common fields for all notification types
    const requiredFields = ['message', 'timestamp'];
    for (const field of requiredFields) {
      if (!data[field]) {
        throw new Error(`Missing required field '${field}' in ${eventType} notification`);
      }
    }

    // Event-specific validation
    switch (eventType) {
      case 'analysis-started':
        if (!data.jobId || !data.status || data.status !== 'started') {
          throw new Error(`Invalid analysis-started notification structure`);
        }
        if (!data.metadata || !data.metadata.assessmentName) {
          throw new Error(`Missing metadata in analysis-started notification`);
        }
        break;

      case 'analysis-complete':
        if (!data.jobId || !data.resultId || !data.status || data.status !== 'completed') {
          throw new Error(`Invalid analysis-complete notification structure`);
        }
        if (!data.metadata || !data.metadata.assessmentName) {
          throw new Error(`Missing metadata in analysis-complete notification`);
        }
        break;

      case 'analysis-failed':
        if (!data.jobId || !data.error || !data.message) {
          throw new Error(`Invalid analysis-failed notification structure`);
        }
        if (!data.metadata || !data.metadata.errorType) {
          throw new Error(`Missing metadata in analysis-failed notification`);
        }
        break;

      default:
        // For other notification types, just check basic structure
        break;
    }

    return true;
  }
}

module.exports = WebSocketClient;
