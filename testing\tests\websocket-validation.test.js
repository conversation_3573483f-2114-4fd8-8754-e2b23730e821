const APIClient = require('../utils/api-client');
const WebSocketClient = require('../utils/websocket-client');
const TestDataGenerator = require('../utils/test-data');

describe('WebSocket Notification Validation Tests', () => {
  let apiClient;
  let wsClient;
  let dataGenerator;
  let testUser;
  let authToken;

  beforeAll(() => {
    apiClient = new APIClient();
    dataGenerator = new TestDataGenerator();
  });

  beforeEach(async () => {
    testUser = dataGenerator.generateUserData();
    wsClient = new WebSocketClient();
    
    // Register and login user
    const registerResponse = await apiClient.register(testUser);
    authToken = registerResponse.data.token;
    apiClient.setAuthToken(authToken);
    
    // Connect and authenticate WebSocket
    await wsClient.connect();
    await wsClient.authenticate(authToken);
  });

  afterEach(async () => {
    // Cleanup WebSocket
    if (wsClient && wsClient.isConnected()) {
      wsClient.disconnect();
    }
    
    // Cleanup user account
    if (authToken) {
      try {
        await apiClient.deleteAccount();
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  });

  describe('WebSocket Connection Validation', () => {
    test('should connect to API Gateway URL', () => {
      expect(wsClient.url).toBe(process.env.WEBSOCKET_URL || 'http://localhost:3000');
    });

    test('should authenticate within timeout', () => {
      expect(wsClient.isAuth()).toBe(true);
      expect(wsClient.authTimeout).toBe(10000); // 10 seconds as per documentation
    });

    test('should have proper Socket.IO configuration', () => {
      const socket = wsClient.socket;
      expect(socket.io.opts.autoConnect).toBe(false);
      expect(socket.io.opts.transports).toEqual(['websocket', 'polling']);
      expect(socket.io.opts.reconnection).toBe(true);
      expect(socket.io.opts.reconnectionAttempts).toBe(5);
      expect(socket.io.opts.timeout).toBe(20000);
    });
  });

  describe('Notification Data Structure Validation', () => {
    test('should validate analysis-started notification structure', async () => {
      // Submit assessment to trigger notification
      const assessmentData = dataGenerator.generateAssessmentData();
      const assessmentResponse = await apiClient.submitAssessment(assessmentData);
      const jobId = assessmentResponse.data.jobId;

      try {
        // Wait for analysis-started notification
        const notification = await wsClient.waitForNotification('analysis-started', 30000);
        
        // Validate notification structure
        expect(notification.type).toBe('analysis-started');
        expect(notification.data).toBeDefined();
        expect(notification.timestamp).toBeDefined();
        
        const data = notification.data;
        
        // Required fields according to WebSocket manual
        expect(data.jobId).toBeDefined();
        expect(data.status).toBe('started');
        expect(data.message).toBeDefined();
        expect(data.timestamp).toBeDefined();
        
        // Metadata validation
        expect(data.metadata).toBeDefined();
        expect(data.metadata.assessmentName).toBeDefined();
        expect(data.metadata.estimatedProcessingTime).toBeDefined();
        
        console.log('✅ analysis-started notification structure is valid');
      } catch (error) {
        if (error.message.includes('Timeout')) {
          console.warn('⚠️ Notification timeout - this may be expected if processing takes longer');
        } else {
          throw error;
        }
      }
    }, 60000);

    test('should validate analysis-complete notification structure', async () => {
      // Submit assessment to trigger notification
      const assessmentData = dataGenerator.generateAssessmentData();
      const assessmentResponse = await apiClient.submitAssessment(assessmentData);
      const jobId = assessmentResponse.data.jobId;

      try {
        // Wait for analysis-complete notification
        const notification = await wsClient.waitForNotification('analysis-complete', 300000);
        
        // Validate notification structure
        expect(notification.type).toBe('analysis-complete');
        expect(notification.data).toBeDefined();
        expect(notification.timestamp).toBeDefined();
        
        const data = notification.data;
        
        // Required fields according to WebSocket manual
        expect(data.jobId).toBeDefined();
        expect(data.resultId).toBeDefined();
        expect(data.status).toBe('completed');
        expect(data.message).toBeDefined();
        expect(data.timestamp).toBeDefined();
        
        // Metadata validation
        expect(data.metadata).toBeDefined();
        expect(data.metadata.assessmentName).toBeDefined();
        expect(data.metadata.processingTime).toBeDefined();
        
        console.log('✅ analysis-complete notification structure is valid');
      } catch (error) {
        if (error.message.includes('Timeout')) {
          console.warn('⚠️ Notification timeout - this may be expected if processing takes longer');
        } else {
          throw error;
        }
      }
    }, 360000); // 6 minutes timeout for complete processing

    test('should handle notification validation warnings gracefully', () => {
      // Test the validation function directly
      const invalidData = {
        // Missing required fields
        jobId: 'test-job-id'
        // Missing: status, message, timestamp, metadata
      };

      expect(() => {
        wsClient.validateNotificationData('analysis-started', invalidData);
      }).toThrow();

      // Test with null data
      expect(() => {
        wsClient.validateNotificationData('analysis-started', null);
      }).toThrow();

      // Test with non-object data
      expect(() => {
        wsClient.validateNotificationData('analysis-started', 'invalid');
      }).toThrow();
    });
  });

  describe('WebSocket Error Handling', () => {
    test('should handle authentication timeout', async () => {
      const newWsClient = new WebSocketClient();
      await newWsClient.connect();
      
      // Don't authenticate - should timeout
      await expect(
        new Promise((resolve, reject) => {
          setTimeout(() => {
            if (!newWsClient.isAuth()) {
              resolve('timeout_as_expected');
            } else {
              reject(new Error('Should have timed out'));
            }
          }, 11000); // Wait longer than auth timeout
        })
      ).resolves.toBe('timeout_as_expected');
      
      newWsClient.disconnect();
    }, 15000);

    test('should handle invalid token authentication', async () => {
      const newWsClient = new WebSocketClient();
      await newWsClient.connect();
      
      await expect(
        newWsClient.authenticate('invalid_token')
      ).rejects.toThrow();
      
      newWsClient.disconnect();
    });
  });
});
