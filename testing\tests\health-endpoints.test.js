const APIClient = require('../utils/api-client');

describe('Health Endpoints Tests', () => {
  let apiClient;

  beforeAll(() => {
    apiClient = new APIClient();
  });

  describe('Global Health Endpoints', () => {
    test('should get main health status', async () => {
      try {
        const response = await apiClient.healthCheck();
        
        expect(response.success).toBe(true);
        expect(response.status).toBe('healthy');
        expect(response.services).toBeDefined();
        expect(response.timestamp).toBeDefined();
        
        // Validate services status
        const services = response.services;
        expect(services.auth).toBeDefined();
        expect(services.assessment).toBeDefined();
        expect(services.archive).toBeDefined();
        expect(services.notification).toBeDefined();
        expect(services.chatbot).toBeDefined();
      } catch (error) {
        if (error.response?.status === 404) {
          console.warn('⚠️ Main health endpoint not implemented yet');
        } else {
          throw error;
        }
      }
    });

    test('should get health metrics', async () => {
      try {
        const response = await apiClient.healthMetrics();
        expect(response).toBeDefined();
      } catch (error) {
        if (error.response?.status === 404) {
          console.warn('⚠️ Health metrics endpoint not implemented yet');
        } else {
          throw error;
        }
      }
    });

    test('should get readiness probe', async () => {
      try {
        const response = await apiClient.healthReady();
        expect(response).toBeDefined();
      } catch (error) {
        if (error.response?.status === 404) {
          console.warn('⚠️ Health ready endpoint not implemented yet');
        } else {
          throw error;
        }
      }
    });

    test('should get liveness probe', async () => {
      try {
        const response = await apiClient.healthLive();
        expect(response).toBeDefined();
      } catch (error) {
        if (error.response?.status === 404) {
          console.warn('⚠️ Health live endpoint not implemented yet');
        } else {
          throw error;
        }
      }
    });
  });

  describe('Assessment Health Endpoints', () => {
    test('should get assessment health status', async () => {
      try {
        const response = await apiClient.assessmentHealth();
        expect(response).toBeDefined();
      } catch (error) {
        if (error.response?.status === 404) {
          console.warn('⚠️ Assessment health endpoint not implemented yet');
        } else {
          throw error;
        }
      }
    });

    test('should get assessment readiness probe', async () => {
      try {
        const response = await apiClient.assessmentHealthReady();
        expect(response).toBeDefined();
      } catch (error) {
        if (error.response?.status === 404) {
          console.warn('⚠️ Assessment health ready endpoint not implemented yet');
        } else if (error.response?.status === 503) {
          console.warn('⚠️ Assessment service not ready (503) - this is expected if dependencies are down');
          expect(error.response.data.status).toBe('not_ready');
          expect(error.response.data.reason).toBeDefined();
        } else {
          throw error;
        }
      }
    });

    test('should get assessment liveness probe', async () => {
      try {
        const response = await apiClient.assessmentHealthLive();
        expect(response).toBeDefined();
      } catch (error) {
        if (error.response?.status === 404) {
          console.warn('⚠️ Assessment health live endpoint not implemented yet');
        } else {
          throw error;
        }
      }
    });

    test('should get assessment queue status', async () => {
      try {
        const response = await apiClient.assessmentHealthQueue();
        expect(response).toBeDefined();
      } catch (error) {
        if (error.response?.status === 404) {
          console.warn('⚠️ Assessment health queue endpoint not implemented yet');
        } else {
          throw error;
        }
      }
    });
  });

  describe('Notification Health Endpoints', () => {
    test('should get notification service health', async () => {
      try {
        const response = await apiClient.getNotificationHealth();

        expect(response.success).toBe(true);
        expect(response.status).toBe('healthy');
        expect(response.service).toBe('notification-service');
        expect(response.timestamp).toBeDefined();

        // Check connections object (actual response structure)
        expect(response.connections).toBeDefined();
        expect(typeof response.connections.total).toBe('number');
        expect(typeof response.connections.authenticated).toBe('number');
        expect(typeof response.connections.users).toBe('number');

        // Check event consumer status
        expect(response.eventConsumer).toBeDefined();
        expect(response.eventConsumer.isConsuming).toBeDefined();
        expect(response.eventConsumer.isConnected).toBeDefined();
      } catch (error) {
        if (error.response?.status === 404) {
          console.warn('⚠️ Notification health endpoint not implemented yet');
        } else {
          throw error;
        }
      }
    });
  });
});
