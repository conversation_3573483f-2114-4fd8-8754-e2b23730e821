#!/usr/bin/env node

const APIClient = require('../utils/api-client');
const WebSocketClient = require('../utils/websocket-client');
const TestDataGenerator = require('../utils/test-data');
const TestLogger = require('../utils/test-logger');
require('dotenv').config();

class WebSocketTest {
  constructor() {
    this.logger = new TestLogger('WebSocket Test');
    this.dataGenerator = new TestDataGenerator();
  }

  async runTest() {
    this.logger.header('WebSocket Connection and Notification Test');
    
    const apiClient = new APIClient();
    const wsClient = new WebSocketClient();
    let userData = null;
    let token = null;

    try {
      // Step 1: Register and login user
      this.logger.step('Register test user');
      const testUser = this.dataGenerator.generateUserData();
      const registerResponse = await apiClient.register(testUser);
      
      if (!registerResponse.success) {
        throw new Error('Registration failed');
      }
      
      userData = registerResponse.data.user;
      token = registerResponse.data.token;
      apiClient.setAuthToken(token);
      this.logger.success(`User registered: ${userData.email}`);

      // Step 2: Test WebSocket connection
      this.logger.step('Connect to WebSocket');
      await wsClient.connect();
      this.logger.success('WebSocket connected successfully');

      // Step 3: Test WebSocket authentication
      this.logger.step('Authenticate WebSocket');
      await wsClient.authenticate(token);
      this.logger.success('WebSocket authenticated successfully');

      // Step 4: Submit assessment to trigger notifications
      this.logger.step('Submit assessment to trigger notifications');
      const assessmentData = this.dataGenerator.generateAssessmentData();
      const assessmentResponse = await apiClient.submitAssessment(assessmentData);
      
      if (!assessmentResponse.success) {
        throw new Error('Assessment submission failed');
      }
      
      const jobId = assessmentResponse.data.jobId;
      this.logger.success(`Assessment submitted with job ID: ${jobId}`);

      // Step 5: Wait for notifications
      this.logger.step('Wait for WebSocket notifications');
      
      try {
        // Wait for analysis-started notification
        this.logger.info('Waiting for analysis-started notification...');
        const startedNotification = await wsClient.waitForNotification('analysis-started', 30000);
        this.logger.success('Received analysis-started notification');
        this.logger.json('Started Notification', startedNotification.data);

        // Wait for analysis-complete notification
        this.logger.info('Waiting for analysis-complete notification...');
        const completeNotification = await wsClient.waitForNotification('analysis-complete', 300000);
        this.logger.success('Received analysis-complete notification');
        this.logger.json('Complete Notification', completeNotification.data);

      } catch (notificationError) {
        this.logger.warning('Notification timeout - this may be expected if processing takes longer');
        
        // Show all received notifications
        const allNotifications = wsClient.getNotifications();
        this.logger.info(`Received ${allNotifications.length} notifications total`);
        allNotifications.forEach((notification, index) => {
          this.logger.info(`Notification ${index + 1}: ${notification.type}`);
          this.logger.json(`Notification ${index + 1} Data`, notification.data);
        });
      }

      // Step 6: Test connection states
      this.logger.step('Test connection states');
      this.logger.info(`WebSocket connected: ${wsClient.isConnected()}`);
      this.logger.info(`WebSocket authenticated: ${wsClient.isAuth()}`);

      // Step 7: Test disconnect and reconnect
      this.logger.step('Test disconnect and reconnect');
      wsClient.disconnect();
      this.logger.success('WebSocket disconnected');
      
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      
      await wsClient.connect();
      this.logger.success('WebSocket reconnected');
      
      await wsClient.authenticate(token);
      this.logger.success('WebSocket re-authenticated');

      // Step 8: Cleanup
      this.logger.step('Cleanup');
      wsClient.disconnect();
      
      try {
        await apiClient.deleteAccount();
        this.logger.success('Test user account deleted');
      } catch (cleanupError) {
        this.logger.warning('Account cleanup failed (endpoint may not be implemented)');
      }

      this.logger.footer();
      this.logger.success('WebSocket test completed successfully');

    } catch (error) {
      this.logger.error('WebSocket test failed', error);
      
      // Cleanup on error
      if (wsClient.isConnected()) {
        wsClient.disconnect();
      }
      
      throw error;
    }
  }

  async testConnectionStates() {
    this.logger.header('WebSocket Connection States Test');
    
    const wsClient = new WebSocketClient();
    
    try {
      // Test initial state
      this.logger.step('Test initial state');
      this.logger.info(`Connected: ${wsClient.isConnected()}`);
      this.logger.info(`Authenticated: ${wsClient.isAuth()}`);

      // Test connected but not authenticated state
      this.logger.step('Test connected but not authenticated');
      await wsClient.connect();
      this.logger.info(`Connected: ${wsClient.isConnected()}`);
      this.logger.info(`Authenticated: ${wsClient.isAuth()}`);

      // Test authentication timeout
      this.logger.step('Test authentication timeout');
      try {
        await wsClient.authenticate('invalid-token');
        this.logger.error('Authentication should have failed');
      } catch (authError) {
        this.logger.success('Authentication correctly failed with invalid token');
      }

      // Cleanup
      wsClient.disconnect();
      this.logger.success('Connection states test completed');

    } catch (error) {
      this.logger.error('Connection states test failed', error);
      wsClient.disconnect();
      throw error;
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  const test = new WebSocketTest();
  
  // Run main test
  test.runTest()
    .then(() => {
      console.log('\n');
      // Run connection states test
      return test.testConnectionStates();
    })
    .then(() => {
      console.log('\n✅ All WebSocket tests completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ WebSocket tests failed:', error.message);
      process.exit(1);
    });
}

module.exports = WebSocketTest;
