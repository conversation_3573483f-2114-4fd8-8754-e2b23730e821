/**
 * Validation utilities for testing
 */

class ValidationUtils {
  /**
   * Validate API response structure
   */
  static validateApiResponse(response, expectedFields = []) {
    if (!response || typeof response !== 'object') {
      throw new Error('Response is not an object');
    }

    if (!response.hasOwnProperty('success')) {
      throw new Error('Response missing success field');
    }

    if (response.success && !response.data) {
      throw new Error('Successful response missing data field');
    }

    if (!response.success && !response.error) {
      throw new Error('Failed response missing error field');
    }

    // Validate expected fields in data
    if (response.success && expectedFields.length > 0) {
      expectedFields.forEach(field => {
        if (!response.data.hasOwnProperty(field)) {
          throw new Error(`Response data missing expected field: ${field}`);
        }
      });
    }

    return true;
  }

  /**
   * Validate user data structure
   */
  static validateUserData(userData) {
    const requiredFields = ['id', 'email', 'username', 'user_type', 'is_active'];
    
    requiredFields.forEach(field => {
      if (!userData.hasOwnProperty(field)) {
        throw new Error(`User data missing required field: ${field}`);
      }
    });

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userData.email)) {
      throw new Error('Invalid email format');
    }

    // Validate UUID format for id
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(userData.id)) {
      throw new Error('Invalid UUID format for user id');
    }

    return true;
  }

  /**
   * Validate assessment data structure
   */
  static validateAssessmentData(assessmentData) {
    const requiredSections = ['riasec', 'ocean', 'viaIs'];
    
    requiredSections.forEach(section => {
      if (!assessmentData.hasOwnProperty(section)) {
        throw new Error(`Assessment data missing required section: ${section}`);
      }
    });

    // Validate RIASEC scores
    const riasecFields = ['realistic', 'investigative', 'artistic', 'social', 'enterprising', 'conventional'];
    riasecFields.forEach(field => {
      if (!assessmentData.riasec.hasOwnProperty(field)) {
        throw new Error(`RIASEC missing field: ${field}`);
      }
      const score = assessmentData.riasec[field];
      if (typeof score !== 'number' || score < 0 || score > 100) {
        throw new Error(`Invalid RIASEC score for ${field}: ${score}`);
      }
    });

    // Validate OCEAN scores
    const oceanFields = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism'];
    oceanFields.forEach(field => {
      if (!assessmentData.ocean.hasOwnProperty(field)) {
        throw new Error(`OCEAN missing field: ${field}`);
      }
      const score = assessmentData.ocean[field];
      if (typeof score !== 'number' || score < 0 || score > 100) {
        throw new Error(`Invalid OCEAN score for ${field}: ${score}`);
      }
    });

    // Validate VIA-IS scores (basic check - there are 24 character strengths)
    const viaIsFields = Object.keys(assessmentData.viaIs);
    if (viaIsFields.length < 20) {
      throw new Error('VIA-IS section has too few character strengths');
    }

    viaIsFields.forEach(field => {
      const score = assessmentData.viaIs[field];
      if (typeof score !== 'number' || score < 0 || score > 100) {
        throw new Error(`Invalid VIA-IS score for ${field}: ${score}`);
      }
    });

    return true;
  }

  /**
   * Validate persona profile structure
   */
  static validatePersonaProfile(personaProfile) {
    const requiredFields = ['archetype', 'shortSummary', 'strengths', 'weaknesses', 'careerRecommendation'];
    
    requiredFields.forEach(field => {
      if (!personaProfile.hasOwnProperty(field)) {
        throw new Error(`Persona profile missing required field: ${field}`);
      }
    });

    // Validate arrays
    if (!Array.isArray(personaProfile.strengths)) {
      throw new Error('Strengths must be an array');
    }

    if (!Array.isArray(personaProfile.weaknesses)) {
      throw new Error('Weaknesses must be an array');
    }

    if (!Array.isArray(personaProfile.careerRecommendation)) {
      throw new Error('Career recommendations must be an array');
    }

    // Validate career recommendations structure
    personaProfile.careerRecommendation.forEach((career, index) => {
      if (!career.careerName) {
        throw new Error(`Career recommendation ${index} missing careerName`);
      }
      if (!career.careerProspect) {
        throw new Error(`Career recommendation ${index} missing careerProspect`);
      }
    });

    return true;
  }

  /**
   * Validate WebSocket notification structure
   */
  static validateWebSocketNotification(notification, expectedType) {
    if (!notification || typeof notification !== 'object') {
      throw new Error('Notification is not an object');
    }

    if (!notification.type) {
      throw new Error('Notification missing type field');
    }

    if (expectedType && notification.type !== expectedType) {
      throw new Error(`Expected notification type ${expectedType}, got ${notification.type}`);
    }

    if (!notification.data) {
      throw new Error('Notification missing data field');
    }

    if (!notification.timestamp) {
      throw new Error('Notification missing timestamp field');
    }

    // Validate specific notification types
    switch (notification.type) {
      case 'analysis-started':
        if (!notification.data.jobId) {
          throw new Error('analysis-started notification missing jobId');
        }
        if (notification.data.status !== 'started') {
          throw new Error('analysis-started notification has wrong status');
        }
        break;

      case 'analysis-complete':
        if (!notification.data.jobId) {
          throw new Error('analysis-complete notification missing jobId');
        }
        if (!notification.data.resultId) {
          throw new Error('analysis-complete notification missing resultId');
        }
        if (notification.data.status !== 'completed') {
          throw new Error('analysis-complete notification has wrong status');
        }
        break;

      case 'analysis-failed':
        if (!notification.data.jobId) {
          throw new Error('analysis-failed notification missing jobId');
        }
        if (!notification.data.error) {
          throw new Error('analysis-failed notification missing error');
        }
        break;
    }

    return true;
  }

  /**
   * Validate JWT token structure (basic validation)
   */
  static validateJWTToken(token) {
    if (!token || typeof token !== 'string') {
      throw new Error('Token is not a string');
    }

    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT token format');
    }

    try {
      // Decode header and payload (without verification)
      const header = JSON.parse(atob(parts[0]));
      const payload = JSON.parse(atob(parts[1]));

      if (!header.alg || !header.typ) {
        throw new Error('Invalid JWT header');
      }

      if (!payload.id || !payload.email) {
        throw new Error('Invalid JWT payload - missing required fields');
      }

      if (!payload.exp || payload.exp < Date.now() / 1000) {
        throw new Error('JWT token is expired');
      }

    } catch (error) {
      throw new Error(`JWT validation failed: ${error.message}`);
    }

    return true;
  }

  /**
   * Validate conversation data structure
   */
  static validateConversationData(conversationData) {
    const requiredFields = ['id', 'title', 'context_type', 'status'];
    
    requiredFields.forEach(field => {
      if (!conversationData.hasOwnProperty(field)) {
        throw new Error(`Conversation data missing required field: ${field}`);
      }
    });

    const validContextTypes = ['general', 'assessment', 'career_guidance'];
    if (!validContextTypes.includes(conversationData.context_type)) {
      throw new Error(`Invalid context_type: ${conversationData.context_type}`);
    }

    const validStatuses = ['active', 'archived', 'deleted'];
    if (!validStatuses.includes(conversationData.status)) {
      throw new Error(`Invalid status: ${conversationData.status}`);
    }

    return true;
  }

  /**
   * Validate message data structure
   */
  static validateMessageData(messageData) {
    const requiredFields = ['id', 'content', 'sender_type', 'created_at'];
    
    requiredFields.forEach(field => {
      if (!messageData.hasOwnProperty(field)) {
        throw new Error(`Message data missing required field: ${field}`);
      }
    });

    const validSenderTypes = ['user', 'assistant'];
    if (!validSenderTypes.includes(messageData.sender_type)) {
      throw new Error(`Invalid sender_type: ${messageData.sender_type}`);
    }

    if (!messageData.content || messageData.content.trim().length === 0) {
      throw new Error('Message content cannot be empty');
    }

    return true;
  }
}

module.exports = ValidationUtils;
