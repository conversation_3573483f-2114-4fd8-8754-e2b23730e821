const chalk = require('chalk');

class TestLogger {
  constructor(testName = 'Test') {
    this.testName = testName;
    this.startTime = Date.now();
    this.stepCount = 0;
    this.verbose = process.env.VERBOSE_LOGGING === 'true';
  }

  info(message, data = null) {
    console.log(chalk.blue(`ℹ️  [${this.testName}] ${message}`));
    if (data && this.verbose) {
      console.log(chalk.gray('   Data:', JSON.stringify(data, null, 2)));
    }
  }

  success(message, data = null) {
    console.log(chalk.green(`✅ [${this.testName}] ${message}`));
    if (data && this.verbose) {
      console.log(chalk.gray('   Data:', JSON.stringify(data, null, 2)));
    }
  }

  error(message, error = null) {
    console.error(chalk.red(`❌ [${this.testName}] ${message}`));
    if (error) {
      console.error(chalk.red('   Error:', error.message));
      if (this.verbose && error.stack) {
        console.error(chalk.gray('   Stack:', error.stack));
      }
    }
  }

  warning(message, data = null) {
    console.log(chalk.yellow(`⚠️  [${this.testName}] ${message}`));
    if (data && this.verbose) {
      console.log(chalk.gray('   Data:', JSON.stringify(data, null, 2)));
    }
  }

  step(message) {
    this.stepCount++;
    console.log(chalk.cyan(`📋 [${this.testName}] Step ${this.stepCount}: ${message}`));
  }

  separator() {
    console.log(chalk.gray('─'.repeat(80)));
  }

  header(title) {
    console.log(chalk.bold.magenta(`\n🚀 ${title}`));
    console.log(chalk.gray('═'.repeat(80)));
  }

  footer() {
    const duration = Date.now() - this.startTime;
    console.log(chalk.gray('═'.repeat(80)));
    console.log(chalk.bold.magenta(`✨ Test completed in ${duration}ms`));
  }

  timing(label, startTime) {
    const duration = Date.now() - startTime;
    console.log(chalk.gray(`⏱️  ${label}: ${duration}ms`));
    return duration;
  }

  table(title, data) {
    console.log(chalk.bold.cyan(`\n📊 ${title}`));
    console.table(data);
  }

  progress(current, total, message = '') {
    const percentage = Math.round((current / total) * 100);
    const progressBar = '█'.repeat(Math.round(percentage / 5)) + '░'.repeat(20 - Math.round(percentage / 5));
    console.log(chalk.blue(`📈 [${progressBar}] ${percentage}% ${message}`));
  }

  json(title, data) {
    console.log(chalk.bold.cyan(`\n📄 ${title}`));
    console.log(JSON.stringify(data, null, 2));
  }

  // Test result summary
  summary(results) {
    console.log(chalk.bold.green('\n📋 Test Summary'));
    console.log(chalk.gray('─'.repeat(50)));
    
    const passed = results.filter(r => r.status === 'passed').length;
    const failed = results.filter(r => r.status === 'failed').length;
    const total = results.length;
    
    console.log(chalk.green(`✅ Passed: ${passed}/${total}`));
    console.log(chalk.red(`❌ Failed: ${failed}/${total}`));
    
    if (failed > 0) {
      console.log(chalk.red('\nFailed Tests:'));
      results.filter(r => r.status === 'failed').forEach(test => {
        console.log(chalk.red(`  • ${test.name}: ${test.error}`));
      });
    }
    
    const successRate = Math.round((passed / total) * 100);
    console.log(chalk.bold.cyan(`\n📊 Success Rate: ${successRate}%`));
  }

  // WebSocket specific logging
  wsConnect(url) {
    console.log(chalk.blue(`🔌 Connecting to WebSocket: ${url}`));
  }

  wsConnected() {
    console.log(chalk.green('✅ WebSocket connected'));
  }

  wsAuthenticated(userId) {
    console.log(chalk.green(`🔐 WebSocket authenticated for user: ${userId}`));
  }

  wsNotification(type, data) {
    console.log(chalk.cyan(`📨 WebSocket notification [${type}]:`, data));
  }

  wsDisconnected() {
    console.log(chalk.yellow('🔌 WebSocket disconnected'));
  }

  // API specific logging
  apiRequest(method, url) {
    console.log(chalk.blue(`→ ${method.toUpperCase()} ${url}`));
  }

  apiResponse(status, method, url, duration) {
    const statusColor = status >= 200 && status < 300 ? chalk.green : chalk.red;
    console.log(statusColor(`← ${status} ${method.toUpperCase()} ${url} (${duration}ms)`));
  }

  apiError(error, method, url) {
    console.error(chalk.red(`← ERROR ${method.toUpperCase()} ${url}: ${error.message}`));
  }
}

module.exports = TestLogger;
