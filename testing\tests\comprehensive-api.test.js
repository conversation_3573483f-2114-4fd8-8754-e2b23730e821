const APIClient = require('../utils/api-client');
const WebSocketClient = require('../utils/websocket-client');
const TestDataGenerator = require('../utils/test-data');

describe('Comprehensive API Tests - Extended Coverage', () => {
  let apiClient;
  let wsClient;
  let dataGenerator;
  let testUser;
  let authToken;

  beforeAll(() => {
    apiClient = new APIClient();
    dataGenerator = new TestDataGenerator();
  });

  beforeEach(async () => {
    testUser = dataGenerator.generateUserData();
    wsClient = new WebSocketClient();
    
    // Register and login user
    const registerResponse = await apiClient.register(testUser);
    authToken = registerResponse.data.token;
    apiClient.setAuthToken(authToken);
  });

  afterEach(async () => {
    // Cleanup WebSocket
    if (wsClient && wsClient.isConnected()) {
      wsClient.disconnect();
    }
    
    // Cleanup user account
    if (authToken) {
      try {
        await apiClient.deleteAccount();
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  });

  describe('Extended Authentication Tests', () => {
    test('should change password successfully', async () => {
      const passwordData = dataGenerator.generatePasswordChangeData();
      const response = await apiClient.changePassword(passwordData);
      
      expect(response.success).toBe(true);
      expect(response.message).toContain('Password changed successfully');
    });

    test('should get token balance', async () => {
      const response = await apiClient.getTokenBalance();
      
      expect(response.success).toBe(true);
      expect(response.data.user_id).toBeDefined();
      expect(typeof response.data.token_balance).toBe('number');
    });

    test('should reject password change with wrong current password', async () => {
      const passwordData = {
        currentPassword: 'WrongPassword123!',
        newPassword: 'NewTestPassword123!'
      };
      
      await expect(apiClient.changePassword(passwordData)).rejects.toThrow();
    });
  });

  describe('Schools Management Tests', () => {
    test('should get schools list with pagination', async () => {
      const response = await apiClient.getSchools({ page: 1, limit: 10 });
      
      expect(response.success).toBe(true);
      expect(response.data.schools).toBeDefined();
      expect(Array.isArray(response.data.schools)).toBe(true);
      expect(response.data.pagination).toBeDefined();
      
      // Validate pagination structure
      apiClient.validatePaginationResponse(response);
    });

    test('should create new school', async () => {
      const schoolData = dataGenerator.generateSchoolData();
      const response = await apiClient.createSchool(schoolData);
      
      expect(response.success).toBe(true);
      expect(response.data.school).toBeDefined();
      expect(response.data.school.name).toBe(schoolData.name);
      expect(response.data.school.city).toBe(schoolData.city);
    });

    test('should search schools by name', async () => {
      const response = await apiClient.getSchools({ 
        search: 'SMA', 
        limit: 5,
        useFullText: true 
      });
      
      expect(response.success).toBe(true);
      expect(response.data.schools).toBeDefined();
      expect(response.data.pagination.limit).toBe(5);
    });

    test('should filter schools by city and province', async () => {
      const response = await apiClient.getSchools({ 
        city: 'Jakarta',
        province: 'DKI Jakarta',
        limit: 20
      });
      
      expect(response.success).toBe(true);
      expect(response.data.schools).toBeDefined();
    });
  });

  describe('Extended Archive Tests', () => {
    test('should get user jobs with filtering', async () => {
      const response = await apiClient.getJobs({ 
        page: 1, 
        limit: 10,
        sort: 'created_at',
        order: 'desc'
      });
      
      expect(response.success).toBe(true);
      expect(response.data.jobs).toBeDefined();
      expect(Array.isArray(response.data.jobs)).toBe(true);
      
      // Validate pagination
      apiClient.validatePaginationResponse(response);
    });

    test('should get job statistics', async () => {
      const response = await apiClient.getJobStats();
      
      expect(response.success).toBe(true);
      expect(response.data.total_jobs).toBeDefined();
      expect(typeof response.data.total_jobs).toBe('number');
      expect(response.data.completed_jobs).toBeDefined();
      expect(response.data.success_rate).toBeDefined();
    });

    test('should get user statistics with different scopes', async () => {
      const overviewResponse = await apiClient.getStats({ 
        type: 'user', 
        scope: 'overview' 
      });
      
      expect(overviewResponse.success).toBe(true);
      expect(overviewResponse.data.total_assessments).toBeDefined();
      
      const detailedResponse = await apiClient.getStats({ 
        type: 'user', 
        scope: 'detailed' 
      });
      
      expect(detailedResponse.success).toBe(true);
      expect(detailedResponse.data.assessment_breakdown).toBeDefined();
    });

    test('should filter results by status and assessment name', async () => {
      const response = await apiClient.getResults({ 
        status: 'completed',
        assessment_name: 'AI-Driven Talent Mapping',
        page: 1,
        limit: 5
      });
      
      expect(response.success).toBe(true);
      expect(response.data.results).toBeDefined();
    });
  });

  describe('Extended Chatbot Tests', () => {
    let conversationId;

    beforeEach(async () => {
      const conversationData = dataGenerator.generateChatbotConversationData();
      const conversationResponse = await apiClient.createConversation(conversationData);
      conversationId = conversationResponse.data.id;
    });

    test('should get conversation by id with messages', async () => {
      const response = await apiClient.getConversationById(conversationId, { 
        include_messages: true,
        message_limit: 50
      });
      
      expect(response.success).toBe(true);
      expect(response.data.id).toBe(conversationId);
      expect(response.data.messages).toBeDefined();
    });

    test('should update conversation status', async () => {
      const updateData = { 
        title: "Updated Chat Title", 
        status: "archived",
        metadata: { updated_reason: "Test update" }
      };
      
      const response = await apiClient.updateConversation(conversationId, updateData);
      
      expect(response.success).toBe(true);
      expect(response.data.title).toBe(updateData.title);
      expect(response.data.status).toBe(updateData.status);
    });

    test('should get conversation messages with pagination', async () => {
      // Send a message first
      const messageData = dataGenerator.generateChatMessage();
      await apiClient.sendMessage(conversationId, messageData);
      
      const response = await apiClient.getMessages(conversationId, { 
        page: 1,
        limit: 10,
        include_usage: true
      });
      
      expect(response.success).toBe(true);
      expect(response.data.messages).toBeDefined();
      expect(response.data.pagination).toBeDefined();
    });

    test('should regenerate assistant message', async () => {
      // Send a message first to get assistant response
      const messageData = dataGenerator.generateChatMessage();
      const messageResponse = await apiClient.sendMessage(conversationId, messageData);
      const assistantMessageId = messageResponse.data.assistant_message.id;
      
      const regenerateResponse = await apiClient.regenerateMessage(conversationId, assistantMessageId);
      
      expect(regenerateResponse.message).toBeDefined();
      expect(regenerateResponse.message.id).toBe(assistantMessageId);
      expect(regenerateResponse.usage).toBeDefined();
    });

    test('should delete conversation', async () => {
      const response = await apiClient.deleteConversation(conversationId);
      
      expect(response.success).toBe(true);
      expect(response.message).toContain('deleted successfully');
    });
  });

  describe('Notification Service Tests', () => {
    test('should get notification service health', async () => {
      const response = await apiClient.getNotificationHealth();
      
      expect(response.success).toBe(true);
      expect(response.status).toBe('healthy');
      expect(response.service).toBe('notification-service');
      expect(response.websocket).toBeDefined();
      expect(typeof response.websocket.connections).toBe('number');
    });
  });

  describe('Admin Functionality Tests', () => {
    let adminToken;

    test('should attempt admin login', async () => {
      const adminCredentials = {
        username: 'admin',
        password: 'adminPassword1'
      };

      try {
        const response = await apiClient.adminLogin(adminCredentials);

        expect(response.success).toBe(true);
        expect(response.data.admin).toBeDefined();
        expect(response.data.token).toBeDefined();
        expect(response.data.admin.user_type).toBe('admin');

        adminToken = response.data.token;
      } catch (error) {
        console.log('Admin login test skipped - admin credentials not configured');
        return;
      }
    });

    test('should register new admin (if authorized)', async () => {
      if (!adminToken) {
        console.log('Admin register test skipped - no admin token');
        return;
      }

      apiClient.setAuthToken(adminToken);
      const adminData = dataGenerator.generateAdminData();

      try {
        const response = await apiClient.adminRegister(adminData);

        expect(response.success).toBe(true);
        expect(response.data.admin).toBeDefined();
        expect(response.data.admin.username).toBe(adminData.username);
        expect(response.data.admin.user_type).toBe('admin');
      } catch (error) {
        console.log('Admin register test skipped - insufficient permissions or endpoint not available');
      }
    });

    test('should update user token balance (if authorized)', async () => {
      if (!adminToken) {
        console.log('Token balance update test skipped - no admin token');
        return;
      }

      // Get current user ID
      apiClient.setAuthToken(authToken);
      const profileResponse = await apiClient.getProfile();
      const userId = profileResponse.data.id;

      // Switch to admin token
      apiClient.setAuthToken(adminToken);
      const balanceData = dataGenerator.generateTokenBalanceData();

      try {
        const response = await apiClient.adminUpdateTokenBalance(userId, balanceData);

        expect(response.success).toBe(true);
        expect(response.data.user_id).toBe(userId);
        expect(response.data.amount).toBe(balanceData.amount);
        expect(response.data.operation).toBe(balanceData.operation);
      } catch (error) {
        console.log('Token balance update test skipped - endpoint not available or insufficient permissions');
      }
    });

    afterAll(async () => {
      // Restore user token
      if (authToken) {
        apiClient.setAuthToken(authToken);
      }
    });
  });

  describe('Assessment Integration Tests', () => {
    test('should check assessment readiness for chatbot', async () => {
      const profileResponse = await apiClient.getProfile();
      const userId = profileResponse.data.id;

      try {
        const response = await apiClient.checkAssessmentReady(userId);

        expect(response.has_assessment).toBeDefined();
        expect(response.ready_for_chatbot).toBeDefined();
        expect(typeof response.has_assessment).toBe('boolean');
        expect(typeof response.ready_for_chatbot).toBe('boolean');
      } catch (error) {
        console.log('Assessment ready check skipped - endpoint may not be available');
      }
    });

    test('should auto-initialize chatbot conversation', async () => {
      try {
        const response = await apiClient.autoInitializeChatbot();

        expect(response.success).toBe(true);
        expect(response.data.conversation).toBeDefined();
        expect(response.data.welcome_message).toBeDefined();
        expect(response.data.suggestions).toBeDefined();
        expect(Array.isArray(response.data.suggestions)).toBe(true);
      } catch (error) {
        console.log('Auto-initialize chatbot skipped - endpoint may not be available or no assessment found');
      }
    });

    test('should create conversation from assessment', async () => {
      const conversationData = dataGenerator.generateConversationFromAssessmentData();

      try {
        const response = await apiClient.createConversationFromAssessment(conversationData);

        expect(response.success).toBe(true);
        expect(response.data.conversation).toBeDefined();
        expect(response.data.conversation.context_type).toBe('assessment');

        if (conversationData.auto_start_message) {
          expect(response.data.welcome_message).toBeDefined();
        }
      } catch (error) {
        console.log('Create conversation from assessment skipped - endpoint may not be available or assessment not found');
      }
    });
  });

  describe('Response Format Validation Tests', () => {
    test('should validate all success responses follow specification', async () => {
      const responses = [
        await apiClient.getProfile(),
        await apiClient.getResults(),
        await apiClient.getConversations(),
        await apiClient.healthCheck()
      ];

      responses.forEach(response => {
        expect(() => apiClient.validateSuccessResponse(response)).not.toThrow();
        expect(response.success).toBe(true);
        expect(response.data).toBeDefined();
      });
    });

    test('should validate pagination responses', async () => {
      const paginatedResponses = [
        await apiClient.getResults(),
        await apiClient.getJobs(),
        await apiClient.getConversations(),
        await apiClient.getSchools()
      ];

      paginatedResponses.forEach(response => {
        if (response.data.pagination) {
          expect(() => apiClient.validatePaginationResponse(response)).not.toThrow();
        }
      });
    });

    test('should validate error response format', async () => {
      try {
        await apiClient.login({ email: '<EMAIL>', password: 'wrong' });
      } catch (error) {
        const errorResponse = error.response?.data;
        if (errorResponse) {
          expect(() => apiClient.validateErrorResponse(errorResponse)).not.toThrow();
          expect(errorResponse.success).toBe(false);
          expect(errorResponse.error).toBeDefined();
          expect(errorResponse.error.code).toBeDefined();
          expect(errorResponse.error.message).toBeDefined();
          expect(errorResponse.error.timestamp).toBeDefined();
        }
      }
    });
  });
});
