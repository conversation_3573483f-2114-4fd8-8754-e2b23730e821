# Troubleshooting Guide

Panduan untuk mengatasi masalah umum dalam testing ATMA Backend.

## 🚨 Common Issues

### 1. Connection Refused Errors

**Error:**
```
Error: connect ECONNREFUSED 127.0.0.1:3000
```

**Causes & Solutions:**

- **Backend services not running**
  ```bash
  # Check if services are running
  curl http://localhost:3000/api/health
  
  # Start backend services if needed
  # (refer to main project documentation)
  ```

- **Wrong API URL in configuration**
  ```bash
  # Check .env file
  cat .env | grep API_BASE_URL
  
  # Update if necessary
  API_BASE_URL=http://localhost:3000/api
  ```

- **Port conflicts**
  ```bash
  # Check what's running on port 3000
  netstat -tulpn | grep :3000
  lsof -i :3000
  ```

### 2. WebSocket Connection Issues

**Error:**
```
❌ WebSocket connection error: Error: xhr poll error
```

**Causes & Solutions:**

- **Notification service not running**
  ```bash
  # Check notification service health
  curl http://localhost:3000/api/notifications/health
  ```

- **CORS issues**
  - WebSocket service should allow all origins in development
  - Check notification service CORS configuration

- **Firewall blocking WebSocket**
  ```bash
  # Test WebSocket connection manually
  curl -i -N -H "Connection: Upgrade" \
       -H "Upgrade: websocket" \
       -H "Sec-WebSocket-Key: test" \
       -H "Sec-WebSocket-Version: 13" \
       http://localhost:3000/socket.io/
  ```

### 3. Authentication Failures

**Error:**
```
🔐 WebSocket auth error: Invalid token
```

**Causes & Solutions:**

- **Token expired**
  - JWT tokens have expiration time
  - Re-login to get fresh token

- **Invalid token format**
  ```javascript
  // Check token format
  const token = "your-jwt-token";
  const parts = token.split('.');
  console.log('Token parts:', parts.length); // Should be 3
  
  // Decode payload (without verification)
  const payload = JSON.parse(atob(parts[1]));
  console.log('Token payload:', payload);
  console.log('Expires at:', new Date(payload.exp * 1000));
  ```

- **Service authentication configuration**
  - Check JWT secret consistency across services
  - Verify token validation logic

### 4. Assessment Processing Timeouts

**Error:**
```
Error: Assessment did not complete within timeout
```

**Causes & Solutions:**

- **Assessment service overloaded**
  ```bash
  # Check assessment service health
  curl http://localhost:3000/api/assessment/health
  ```

- **Increase timeout in configuration**
  ```env
  # In .env file
  ASSESSMENT_TIMEOUT_MS=600000  # 10 minutes
  ```

- **Check assessment queue status**
  ```bash
  # Check queue health
  curl http://localhost:3000/api/assessment/health/queue
  ```

### 5. Test Data Validation Errors

**Error:**
```
ValidationError: RIASEC missing field: realistic
```

**Causes & Solutions:**

- **Incomplete test data generation**
  ```javascript
  // Check generated data
  const data = dataGenerator.generateAssessmentData();
  console.log('Generated data:', JSON.stringify(data, null, 2));
  ```

- **API schema changes**
  - Update test data generator to match current API schema
  - Check API documentation for required fields

### 6. Rate Limiting Issues

**Error:**
```
Error: Request failed with status code 429
```

**Causes & Solutions:**

- **Too many requests**
  ```bash
  # Check rate limit headers
  curl -I http://localhost:3000/api/auth/profile \
       -H "Authorization: Bearer your-token"
  ```

- **Add delays between requests**
  ```javascript
  // Add delay in test code
  await new Promise(resolve => setTimeout(resolve, 1000));
  ```

- **Use different test accounts**
  - Rate limits are often per-user
  - Generate unique emails for each test run

## 🔍 Debugging Techniques

### 1. Enable Verbose Logging

```bash
# Set in .env file
VERBOSE_LOGGING=true

# Or run with environment variable
VERBOSE_LOGGING=true npm run test:full-scenario
```

### 2. Check Service Health

```bash
# API Gateway health
curl http://localhost:3000/api/health

# Individual service health
curl http://localhost:3000/api/auth/health
curl http://localhost:3000/api/assessment/health
curl http://localhost:3000/api/archive/health
curl http://localhost:3000/api/notifications/health
curl http://localhost:3000/api/chatbot/health
```

### 3. Monitor Network Traffic

```bash
# Use tcpdump to monitor HTTP traffic
sudo tcpdump -i lo -A -s 0 'port 3000'

# Or use Wireshark for GUI monitoring
```

### 4. Check Database Connections

```bash
# If using PostgreSQL
psql -h localhost -U your_user -d your_database -c "SELECT 1;"

# If using MongoDB
mongo --eval "db.runCommand('ping')"
```

### 5. Inspect WebSocket Messages

```javascript
// Enable Socket.IO debug mode in browser console
localStorage.debug = 'socket.io-client:socket';

// Or in Node.js
process.env.DEBUG = 'socket.io-client:socket';
```

## 🛠️ Manual Testing Commands

### Test Individual Components

```bash
# Test API endpoints only
node tests/user-flow-test.js

# Test WebSocket only
node tests/websocket-test.js

# Test specific utility
node examples/manual-test-example.js utils
```

### Test with Custom Data

```javascript
// Create custom test
const APIClient = require('./utils/api-client');
const client = new APIClient();

// Test specific endpoint
client.register({
  email: '<EMAIL>',
  password: 'DebugPass123!',
  username: 'debuguser'
}).then(response => {
  console.log('Registration response:', response);
}).catch(error => {
  console.error('Registration error:', error.response?.data || error.message);
});
```

### Check Environment Variables

```bash
# Display current configuration
node -e "
require('dotenv').config();
console.log('API_BASE_URL:', process.env.API_BASE_URL);
console.log('WEBSOCKET_URL:', process.env.WEBSOCKET_URL);
console.log('TIMEOUT_MS:', process.env.TIMEOUT_MS);
console.log('VERBOSE_LOGGING:', process.env.VERBOSE_LOGGING);
"
```

## 📊 Performance Issues

### 1. Slow Test Execution

**Causes & Solutions:**

- **Network latency**
  ```bash
  # Test network latency
  ping localhost
  curl -w "@curl-format.txt" -o /dev/null -s http://localhost:3000/api/health
  ```

- **Database performance**
  - Check database query performance
  - Monitor database connection pool usage

- **Assessment processing time**
  - AI/ML processing can be slow
  - Consider using mock responses for faster testing

### 2. Memory Leaks

**Symptoms:**
- Tests become slower over time
- Node.js process memory usage increases

**Solutions:**
```javascript
// Proper cleanup in tests
afterEach(async () => {
  if (wsClient && wsClient.isConnected()) {
    wsClient.disconnect();
  }
  
  // Clear any timers
  clearTimeout(timeoutId);
  
  // Remove event listeners
  process.removeAllListeners('unhandledRejection');
});
```

### 3. Resource Exhaustion

**Error:**
```
Error: EMFILE: too many open files
```

**Solutions:**
```bash
# Increase file descriptor limit
ulimit -n 4096

# Check current limits
ulimit -a
```

## 🔧 Configuration Issues

### 1. Environment Variables Not Loading

```bash
# Check if .env file exists
ls -la .env

# Check file permissions
chmod 644 .env

# Verify content
cat .env | grep -v '^#' | grep -v '^$'
```

### 2. Port Conflicts

```bash
# Find processes using ports
lsof -i :3000
lsof -i :3005

# Kill conflicting processes if needed
kill -9 <PID>
```

### 3. SSL/TLS Issues (Production)

```bash
# Test SSL connection
openssl s_client -connect your-domain.com:443

# Check certificate validity
curl -vI https://your-domain.com/api/health
```

## 📞 Getting Help

### 1. Enable Debug Mode

```bash
# Run with maximum debugging
DEBUG=* VERBOSE_LOGGING=true npm run test:full-scenario
```

### 2. Collect System Information

```bash
# System info script
echo "Node.js version: $(node --version)"
echo "NPM version: $(npm --version)"
echo "OS: $(uname -a)"
echo "Memory: $(free -h 2>/dev/null || vm_stat)"
echo "Disk: $(df -h .)"
```

### 3. Create Minimal Reproduction

```javascript
// minimal-repro.js
const APIClient = require('./utils/api-client');

async function reproduce() {
  const client = new APIClient();
  
  try {
    // Minimal steps to reproduce the issue
    const response = await client.healthCheck();
    console.log('Health check:', response);
  } catch (error) {
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

reproduce();
```

### 4. Log Collection

```bash
# Collect logs
mkdir -p debug-logs
npm run test:full-scenario 2>&1 | tee debug-logs/test-output.log

# Include system logs if needed
journalctl -u your-service --since "1 hour ago" > debug-logs/service.log
```

## 📋 Checklist for Issue Resolution

- [ ] Check service health endpoints
- [ ] Verify environment configuration
- [ ] Test network connectivity
- [ ] Check authentication tokens
- [ ] Monitor resource usage
- [ ] Review error logs
- [ ] Test with minimal reproduction
- [ ] Check for known issues in documentation
- [ ] Verify API schema compatibility
- [ ] Test individual components separately
