#!/usr/bin/env node

const APIClient = require('../utils/api-client');
const WebSocketClient = require('../utils/websocket-client');
const TestDataGenerator = require('../utils/test-data');
const TestLogger = require('../utils/test-logger');
require('dotenv').config();

class FullScenarioTest {
  constructor() {
    this.logger = new TestLogger('Full Scenario Test');
    this.dataGenerator = new TestDataGenerator();
    this.users = [];
    this.cleanupAfterTest = process.env.CLEANUP_AFTER_TEST === 'true';
    this.assessmentTimeout = parseInt(process.env.ASSESSMENT_TIMEOUT_MS) || 300000; // 5 minutes
  }

  async runTest() {
    this.logger.header('ATMA Backend Full Scenario Test - 2 Users');
    
    try {
      // Generate test scenarios
      const scenarios = this.dataGenerator.generateTestScenarios();
      
      // Run tests for both users in parallel
      const userPromises = scenarios.map((scenario, index) => 
        this.runUserScenario(scenario, index + 1)
      );
      
      const results = await Promise.allSettled(userPromises);
      
      // Process results
      const testResults = results.map((result, index) => ({
        name: scenarios[index].name,
        status: result.status === 'fulfilled' ? 'passed' : 'failed',
        error: result.status === 'rejected' ? result.reason.message : null,
        data: result.status === 'fulfilled' ? result.value : null
      }));
      
      this.logger.summary(testResults);
      this.logger.footer();
      
      // Exit with appropriate code
      const hasFailures = testResults.some(r => r.status === 'failed');
      process.exit(hasFailures ? 1 : 0);
      
    } catch (error) {
      this.logger.error('Test suite failed', error);
      process.exit(1);
    }
  }

  async runUserScenario(scenario, userNumber) {
    const logger = new TestLogger(`User ${userNumber}`);
    const apiClient = new APIClient();
    const wsClient = new WebSocketClient();
    
    let userData = null;
    let token = null;
    let jobId = null;
    let resultId = null;
    let conversationId = null;

    try {
      logger.header(`Testing Scenario: ${scenario.name}`);

      // Step 1: Register user
      logger.step('Register user with random email');
      const registerResponse = await apiClient.register(scenario.userData);
      
      if (!registerResponse.success) {
        throw new Error('Registration failed');
      }
      
      userData = registerResponse.data.user;
      token = registerResponse.data.token;
      logger.success(`User registered: ${userData.email}`);

      // Step 2: Login user
      logger.step('Login user');
      const loginResponse = await apiClient.login({
        email: scenario.userData.email,
        password: scenario.userData.password
      });
      
      if (!loginResponse.success) {
        throw new Error('Login failed');
      }
      
      token = loginResponse.data.token;
      apiClient.setAuthToken(token);
      logger.success('User logged in successfully');

      // Step 3: Connect WebSocket
      logger.step('Connect to WebSocket');
      await wsClient.connect();
      logger.success('WebSocket connected');

      // Step 4: Authenticate WebSocket
      logger.step('Authenticate WebSocket');
      await wsClient.authenticate(token);
      logger.success('WebSocket authenticated');

      // Step 5: Update profile
      logger.step('Update user profile');
      const profileResponse = await apiClient.updateProfile(scenario.profileUpdate);
      
      if (!profileResponse.success) {
        throw new Error('Profile update failed');
      }
      
      logger.success('Profile updated successfully');

      // Step 6: Submit assessment
      logger.step('Submit assessment');
      const assessmentResponse = await apiClient.submitAssessment(scenario.assessmentData);
      
      if (!assessmentResponse.success) {
        throw new Error('Assessment submission failed');
      }
      
      jobId = assessmentResponse.data.jobId;
      logger.success(`Assessment submitted with job ID: ${jobId}`);

      // Step 7: Wait for WebSocket notification
      logger.step('Wait for assessment completion notification');
      const startTime = Date.now();
      
      try {
        // Wait for analysis-started notification
        const startedNotification = await wsClient.waitForNotification('analysis-started', 30000);
        logger.success('Received analysis-started notification');
        
        // Wait for analysis-complete notification
        const completeNotification = await wsClient.waitForNotification('analysis-complete', this.assessmentTimeout);
        logger.success('Received analysis-complete notification');
        
        resultId = completeNotification.data.resultId;
        const processingTime = Date.now() - startTime;
        logger.timing('Assessment processing time', startTime);
        
      } catch (notificationError) {
        logger.warning('WebSocket notification timeout, checking status via API');
        
        // Fallback: Check status via API
        let attempts = 0;
        const maxAttempts = 30; // 5 minutes with 10-second intervals
        
        while (attempts < maxAttempts) {
          const statusResponse = await apiClient.getAssessmentStatus(jobId);
          
          if (statusResponse.data.status === 'completed') {
            resultId = statusResponse.data.resultId;
            logger.success('Assessment completed (verified via API)');
            break;
          } else if (statusResponse.data.status === 'failed') {
            throw new Error('Assessment processing failed');
          }
          
          attempts++;
          await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
        }
        
        if (!resultId) {
          throw new Error('Assessment did not complete within timeout');
        }
      }

      // Step 8: Get profile persona
      logger.step('Retrieve assessment results');
      const resultResponse = await apiClient.getResultById(resultId);
      
      if (!resultResponse.success) {
        throw new Error('Failed to retrieve assessment results');
      }
      
      const personaProfile = resultResponse.data.persona_profile;
      logger.success(`Assessment results retrieved - Archetype: ${personaProfile.archetype}`);
      logger.json('Persona Profile', personaProfile);

      // Step 9: Test chatbot
      logger.step('Test chatbot functionality');
      
      // Create conversation
      const conversationResponse = await apiClient.createConversation(
        this.dataGenerator.generateChatbotConversationData()
      );
      
      if (!conversationResponse.success) {
        throw new Error('Failed to create conversation');
      }
      
      conversationId = conversationResponse.data.id;
      logger.success(`Conversation created: ${conversationId}`);
      
      // Send message
      const messageData = this.dataGenerator.generateChatMessage();
      const messageResponse = await apiClient.sendMessage(conversationId, messageData);
      
      if (!messageResponse.success) {
        throw new Error('Failed to send message');
      }
      
      logger.success('Chatbot interaction completed');
      logger.json('Chatbot Response', messageResponse.data.assistant_message);

      // Step 10: Cleanup (delete account if enabled)
      if (this.cleanupAfterTest) {
        logger.step('Cleanup - Delete account');
        try {
          await apiClient.deleteAccount();
          logger.success('Account deleted successfully');
        } catch (cleanupError) {
          logger.warning('Account cleanup failed (endpoint may not be implemented)');
        }
      }

      // Disconnect WebSocket
      wsClient.disconnect();
      logger.success('WebSocket disconnected');

      logger.footer();

      return {
        userId: userData.id,
        email: userData.email,
        archetype: personaProfile.archetype,
        jobId,
        resultId,
        conversationId,
        notifications: wsClient.getNotifications()
      };

    } catch (error) {
      logger.error('User scenario failed', error);

      // Cleanup on error
      if (wsClient.isConnected()) {
        wsClient.disconnect();
      }

      throw error;
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  const test = new FullScenarioTest();
  test.runTest().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = FullScenarioTest;

// Run the test if this file is executed directly
if (require.main === module) {
  const test = new FullScenarioTest();
  test.runTest().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = FullScenarioTest;
