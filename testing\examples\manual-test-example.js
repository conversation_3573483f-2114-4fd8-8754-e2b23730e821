#!/usr/bin/env node

/**
 * Manual Test Example
 * 
 * This file demonstrates how to use the testing utilities manually
 * for debugging or custom testing scenarios.
 */

const APIClient = require('../utils/api-client');
const WebSocketClient = require('../utils/websocket-client');
const TestDataGenerator = require('../utils/test-data');
const TestLogger = require('../utils/test-logger');
const ValidationUtils = require('../utils/validation');
require('dotenv').config();

async function manualTestExample() {
  const logger = new TestLogger('Manual Test Example');
  const apiClient = new APIClient();
  const wsClient = new WebSocketClient();
  const dataGenerator = new TestDataGenerator();

  try {
    logger.header('Manual Test Example - Step by Step');

    // Step 1: Generate test data
    logger.step('Generate test data');
    const userData = dataGenerator.generateUserData();
    const assessmentData = dataGenerator.generateAssessmentData();
    
    logger.info('Generated user data:', userData);
    logger.info('Generated assessment data (sample):', {
      assessmentName: assessmentData.assessmentName,
      riasec: assessmentData.riasec,
      ocean: assessmentData.ocean
    });

    // Step 2: Register user
    logger.step('Register user');
    const registerResponse = await apiClient.register(userData);
    
    // Validate response
    ValidationUtils.validateApiResponse(registerResponse, ['user', 'token']);
    ValidationUtils.validateUserData(registerResponse.data.user);
    ValidationUtils.validateJWTToken(registerResponse.data.token);
    
    logger.success('User registered and validated');
    
    const token = registerResponse.data.token;
    apiClient.setAuthToken(token);

    // Step 3: Connect and authenticate WebSocket
    logger.step('Connect WebSocket');
    await wsClient.connect();
    await wsClient.authenticate(token);
    
    logger.success('WebSocket connected and authenticated');

    // Step 4: Update profile
    logger.step('Update profile');
    const profileUpdate = dataGenerator.generateProfileUpdateData();
    const updateResponse = await apiClient.updateProfile(profileUpdate);
    
    ValidationUtils.validateApiResponse(updateResponse, ['user']);
    logger.success('Profile updated successfully');

    // Step 5: Submit assessment
    logger.step('Submit assessment');
    
    // Validate assessment data before submission
    ValidationUtils.validateAssessmentData(assessmentData);
    
    const assessmentResponse = await apiClient.submitAssessment(assessmentData);
    ValidationUtils.validateApiResponse(assessmentResponse, ['jobId', 'status']);
    
    const jobId = assessmentResponse.data.jobId;
    logger.success(`Assessment submitted with job ID: ${jobId}`);

    // Step 6: Monitor WebSocket notifications
    logger.step('Monitor WebSocket notifications');
    
    // Wait for analysis-started notification
    logger.info('Waiting for analysis-started notification...');
    const startedNotification = await wsClient.waitForNotification('analysis-started', 30000);
    ValidationUtils.validateWebSocketNotification(startedNotification, 'analysis-started');
    logger.success('Received and validated analysis-started notification');

    // Wait for analysis-complete notification (with longer timeout)
    logger.info('Waiting for analysis-complete notification...');
    const completeNotification = await wsClient.waitForNotification('analysis-complete', 300000);
    ValidationUtils.validateWebSocketNotification(completeNotification, 'analysis-complete');
    logger.success('Received and validated analysis-complete notification');

    const resultId = completeNotification.data.resultId;

    // Step 7: Retrieve and validate results
    logger.step('Retrieve assessment results');
    const resultResponse = await apiClient.getResultById(resultId);
    
    ValidationUtils.validateApiResponse(resultResponse);
    ValidationUtils.validatePersonaProfile(resultResponse.data.persona_profile);
    
    logger.success('Assessment results retrieved and validated');
    logger.json('Persona Profile', resultResponse.data.persona_profile);

    // Step 8: Test chatbot
    logger.step('Test chatbot functionality');
    
    // Create conversation
    const conversationData = dataGenerator.generateChatbotConversationData();
    const conversationResponse = await apiClient.createConversation(conversationData);
    
    ValidationUtils.validateApiResponse(conversationResponse);
    ValidationUtils.validateConversationData(conversationResponse.data);
    
    const conversationId = conversationResponse.data.id;
    logger.success(`Conversation created: ${conversationId}`);

    // Send message
    const messageData = dataGenerator.generateChatMessage();
    const messageResponse = await apiClient.sendMessage(conversationId, messageData);
    
    ValidationUtils.validateApiResponse(messageResponse, ['user_message', 'assistant_message']);
    ValidationUtils.validateMessageData(messageResponse.data.user_message);
    ValidationUtils.validateMessageData(messageResponse.data.assistant_message);
    
    logger.success('Chatbot interaction completed and validated');

    // Step 9: Test additional endpoints
    logger.step('Test additional endpoints');
    
    // Get all results
    const allResultsResponse = await apiClient.getResults();
    ValidationUtils.validateApiResponse(allResultsResponse, ['results']);
    logger.info(`User has ${allResultsResponse.data.results.length} assessment results`);

    // Get all conversations
    const conversationsResponse = await apiClient.getConversations();
    ValidationUtils.validateApiResponse(conversationsResponse, ['conversations']);
    logger.info(`User has ${conversationsResponse.data.conversations.length} conversations`);

    // Health check
    const healthResponse = await apiClient.healthCheck();
    ValidationUtils.validateApiResponse(healthResponse);
    logger.success('Health check passed');

    // Step 10: Display summary
    logger.step('Test summary');
    
    const allNotifications = wsClient.getNotifications();
    logger.table('WebSocket Notifications Received', allNotifications.map(n => ({
      Type: n.type,
      Timestamp: new Date(n.timestamp).toISOString(),
      JobId: n.data.jobId || 'N/A',
      ResultId: n.data.resultId || 'N/A'
    })));

    logger.success('All manual tests completed successfully!');

    // Cleanup
    logger.step('Cleanup');
    wsClient.disconnect();
    
    try {
      await apiClient.deleteAccount();
      logger.success('Test account deleted');
    } catch (cleanupError) {
      logger.warning('Account cleanup failed (endpoint may not be implemented)');
    }

    logger.footer();

  } catch (error) {
    logger.error('Manual test failed', error);
    
    // Cleanup on error
    if (wsClient.isConnected()) {
      wsClient.disconnect();
    }
    
    process.exit(1);
  }
}

// Demonstration of individual utility usage
async function demonstrateUtilities() {
  const logger = new TestLogger('Utility Demonstration');
  
  logger.header('Utility Functions Demonstration');

  // API Client demonstration
  logger.step('API Client demonstration');
  const apiClient = new APIClient();
  logger.info(`API Base URL: ${apiClient.baseURL}`);
  logger.info(`Timeout: ${apiClient.timeout}ms`);

  // WebSocket Client demonstration
  logger.step('WebSocket Client demonstration');
  const wsClient = new WebSocketClient();
  logger.info(`WebSocket URL: ${wsClient.url}`);
  logger.info(`Auth Timeout: ${wsClient.authTimeout}ms`);

  // Test Data Generator demonstration
  logger.step('Test Data Generator demonstration');
  const dataGenerator = new TestDataGenerator();
  
  const sampleUser = dataGenerator.generateUserData();
  const sampleAssessment = dataGenerator.generateAssessmentData();
  const sampleMessage = dataGenerator.generateChatMessage();
  
  logger.json('Sample User Data', sampleUser);
  logger.json('Sample Assessment (RIASEC only)', sampleAssessment.riasec);
  logger.json('Sample Chat Message', sampleMessage);

  // Validation demonstration
  logger.step('Validation demonstration');
  
  try {
    ValidationUtils.validateUserData({
      id: '550e8400-e29b-41d4-a716-446655440000',
      email: '<EMAIL>',
      username: 'testuser',
      user_type: 'user',
      is_active: true
    });
    logger.success('User data validation passed');
  } catch (validationError) {
    logger.error('User data validation failed', validationError);
  }

  try {
    ValidationUtils.validateAssessmentData(sampleAssessment);
    logger.success('Assessment data validation passed');
  } catch (validationError) {
    logger.error('Assessment data validation failed', validationError);
  }

  logger.footer();
}

// Run examples based on command line argument
if (require.main === module) {
  const command = process.argv[2] || 'full';
  
  switch (command) {
    case 'full':
      manualTestExample().catch(error => {
        console.error('Manual test failed:', error);
        process.exit(1);
      });
      break;
      
    case 'utils':
      demonstrateUtilities().catch(error => {
        console.error('Utility demonstration failed:', error);
        process.exit(1);
      });
      break;
      
    default:
      console.log('Usage: node manual-test-example.js [full|utils]');
      console.log('  full  - Run full manual test example (default)');
      console.log('  utils - Demonstrate utility functions');
      process.exit(0);
  }
}

module.exports = { manualTestExample, demonstrateUtilities };
