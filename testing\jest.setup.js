// Jest setup file
require('dotenv').config();

// Global test timeout
jest.setTimeout(300000); // 5 minutes

// Global test setup
beforeAll(() => {
  console.log('🧪 Starting ATMA Backend Test Suite');
  console.log(`📡 API Base URL: ${process.env.API_BASE_URL}`);
  console.log(`🔌 WebSocket URL: ${process.env.WEBSOCKET_URL}`);
});

afterAll(() => {
  console.log('✅ ATMA Backend Test Suite Completed');
});

// Global error handler
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});
