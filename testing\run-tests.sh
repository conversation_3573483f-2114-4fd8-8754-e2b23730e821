#!/bin/bash

# ATMA Backend Testing Script
# This script runs comprehensive tests for the ATMA backend system

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if service is running
check_service() {
    local url=$1
    local service_name=$2
    
    print_status "Checking $service_name at $url..."
    
    if curl -s -f "$url" > /dev/null 2>&1; then
        print_success "$service_name is running"
        return 0
    else
        print_error "$service_name is not responding at $url"
        return 1
    fi
}

# Function to run test with timeout
run_test_with_timeout() {
    local test_command=$1
    local test_name=$2
    local timeout=${3:-300}  # Default 5 minutes
    
    print_status "Running $test_name..."
    
    if timeout $timeout $test_command; then
        print_success "$test_name completed successfully"
        return 0
    else
        print_error "$test_name failed or timed out"
        return 1
    fi
}

# Main execution
main() {
    echo "🚀 ATMA Backend Testing Suite"
    echo "=============================="
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ]; then
        print_error "package.json not found. Please run this script from the testing directory."
        exit 1
    fi
    
    # Check if dependencies are installed
    if [ ! -d "node_modules" ]; then
        print_status "Installing dependencies..."
        npm install
    fi
    
    # Check if .env file exists
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Copying from .env.example..."
        cp .env.example .env
        print_warning "Please review and update .env file if needed."
    fi
    
    # Load environment variables
    if [ -f ".env" ]; then
        export $(cat .env | grep -v '^#' | xargs)
    fi
    
    # Set default values if not provided
    API_BASE_URL=${API_BASE_URL:-"http://localhost:3000/api"}
    WEBSOCKET_URL=${WEBSOCKET_URL:-"http://localhost:3000"}
    
    print_status "Configuration:"
    print_status "  API Base URL: $API_BASE_URL"
    print_status "  WebSocket URL: $WEBSOCKET_URL"
    
    # Check if backend services are running
    print_status "Checking backend services..."
    
    if ! check_service "${API_BASE_URL%/api}/health" "API Gateway"; then
        print_error "API Gateway is not running. Please start the backend services first."
        exit 1
    fi
    
    if ! check_service "${API_BASE_URL%/api}/api/notifications/health" "Notification Service"; then
        print_warning "Notification Service health check failed. WebSocket tests may fail."
    fi
    
    # Run tests based on command line arguments
    case "${1:-all}" in
        "full"|"full-scenario")
            print_status "Running full scenario test (2 users)..."
            run_test_with_timeout "npm run test:full-scenario" "Full Scenario Test" 600
            ;;
        "user-flow")
            print_status "Running user flow test..."
            run_test_with_timeout "npm run test:user-flow" "User Flow Test" 300
            ;;
        "websocket"|"ws")
            print_status "Running WebSocket test..."
            run_test_with_timeout "npm run test:websocket" "WebSocket Test" 300
            ;;
        "jest")
            print_status "Running Jest test suite..."
            run_test_with_timeout "npm test" "Jest Test Suite" 600
            ;;
        "all")
            print_status "Running all tests..."
            
            # Run individual tests
            run_test_with_timeout "npm run test:user-flow" "User Flow Test" 300
            echo ""
            
            run_test_with_timeout "npm run test:websocket" "WebSocket Test" 300
            echo ""
            
            run_test_with_timeout "npm run test:full-scenario" "Full Scenario Test" 600
            echo ""
            
            run_test_with_timeout "npm test" "Jest Test Suite" 600
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [test-type]"
            echo ""
            echo "Test types:"
            echo "  all          - Run all tests (default)"
            echo "  full         - Run full scenario test (2 users)"
            echo "  user-flow    - Run user flow test"
            echo "  websocket    - Run WebSocket test"
            echo "  jest         - Run Jest test suite"
            echo "  help         - Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    # Run all tests"
            echo "  $0 full              # Run full scenario test"
            echo "  $0 websocket         # Run WebSocket test only"
            exit 0
            ;;
        *)
            print_error "Unknown test type: $1"
            print_status "Use '$0 help' to see available options"
            exit 1
            ;;
    esac
    
    print_success "All tests completed!"
    echo ""
    echo "📊 Test Summary:"
    echo "  - Check the output above for detailed results"
    echo "  - Failed tests will be marked with ❌"
    echo "  - Successful tests will be marked with ✅"
    echo ""
    echo "🔍 For debugging:"
    echo "  - Set VERBOSE_LOGGING=true in .env for detailed logs"
    echo "  - Check individual test files in tests/ directory"
    echo "  - Verify backend services are running and accessible"
}

# Run main function with all arguments
main "$@"
