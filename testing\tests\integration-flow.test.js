const APIClient = require('../utils/api-client');
const WebSocketClient = require('../utils/websocket-client');
const TestDataGenerator = require('../utils/test-data');

describe('Integration Flow Tests - Complete User Journeys', () => {
  let apiClient;
  let wsClient;
  let dataGenerator;
  let testUser;
  let authToken;

  beforeAll(() => {
    apiClient = new APIClient();
    dataGenerator = new TestDataGenerator();
  });

  beforeEach(async () => {
    testUser = dataGenerator.generateUserData();
    wsClient = new WebSocketClient();
    
    // Register and login user
    const registerResponse = await apiClient.register(testUser);
    authToken = registerResponse.data.token;
    apiClient.setAuthToken(authToken);
  });

  afterEach(async () => {
    // Cleanup WebSocket
    if (wsClient && wsClient.isConnected()) {
      wsClient.disconnect();
    }
    
    // Cleanup user account
    if (authToken) {
      try {
        await apiClient.deleteAccount();
      } catch (error) {
        // Ignore cleanup errors
      }
    }
  });

  describe('Complete Assessment to Chatbot Flow', () => {
    test('should complete full assessment to chatbot integration', async () => {
      // Step 1: Setup WebSocket for real-time notifications
      await wsClient.connect();
      await wsClient.authenticate(authToken);
      expect(wsClient.isAuth()).toBe(true);
      
      // Step 2: Submit assessment
      const assessmentData = dataGenerator.generateAssessmentData();
      const submitResponse = await apiClient.submitAssessment(assessmentData);
      expect(submitResponse.success).toBe(true);
      const jobId = submitResponse.data.jobId;
      
      // Step 3: Receive analysis started notification
      const startedNotification = await wsClient.waitForNotification('analysis-started', 30000);
      expect(startedNotification.data.jobId).toBe(jobId);
      expect(startedNotification.data.status).toBe('started');
      
      // Step 4: Monitor assessment status via API
      let statusResponse = await apiClient.getAssessmentStatus(jobId);
      expect(statusResponse.success).toBe(true);
      expect(['queued', 'processing', 'completed']).toContain(statusResponse.data.status);
      
      // Step 5: Wait for completion notification
      const completeNotification = await wsClient.waitForNotification('analysis-complete', 300000);
      expect(completeNotification.data.jobId).toBe(jobId);
      expect(completeNotification.data.resultId).toBeDefined();
      const resultId = completeNotification.data.resultId;
      
      // Step 6: Verify assessment result
      const resultResponse = await apiClient.getResultById(resultId);
      expect(resultResponse.success).toBe(true);
      expect(resultResponse.data.persona_profile).toBeDefined();
      expect(resultResponse.data.persona_profile.archetype).toBeDefined();
      expect(resultResponse.data.persona_profile.careerRecommendation).toBeDefined();
      
      // Step 7: Check if assessment is ready for chatbot
      const profileResponse = await apiClient.getProfile();
      const userId = profileResponse.data.id;
      
      try {
        const readyResponse = await apiClient.checkAssessmentReady(userId);
        expect(readyResponse.has_assessment).toBe(true);
        expect(readyResponse.ready_for_chatbot).toBe(true);
        expect(readyResponse.assessment_id).toBe(resultId);
      } catch (error) {
        console.log('Assessment ready check skipped - endpoint may not be available');
      }
      
      // Step 8: Create conversation from assessment
      try {
        const conversationFromAssessment = await apiClient.createConversationFromAssessment({
          assessment_id: resultId,
          title: "AI Career Guidance Based on Assessment",
          auto_start_message: true
        });
        
        expect(conversationFromAssessment.success).toBe(true);
        expect(conversationFromAssessment.data.conversation).toBeDefined();
        expect(conversationFromAssessment.data.welcome_message).toBeDefined();
        expect(conversationFromAssessment.data.suggestions).toBeDefined();
        
        const conversationId = conversationFromAssessment.data.conversation.id;
        
        // Step 9: Get conversation suggestions
        const suggestionsResponse = await apiClient.getConversationSuggestions(conversationId);
        expect(suggestionsResponse.success).toBe(true);
        expect(Array.isArray(suggestionsResponse.data.suggestions)).toBe(true);
        expect(suggestionsResponse.data.context.assessment_based).toBe(true);
        
        // Step 10: Send message using suggested question
        const suggestion = suggestionsResponse.data.suggestions[0];
        const messageResponse = await apiClient.sendMessage(conversationId, {
          content: suggestion,
          content_type: "text"
        });
        
        expect(messageResponse.success).toBe(true);
        expect(messageResponse.data.user_message).toBeDefined();
        expect(messageResponse.data.assistant_message).toBeDefined();
        expect(messageResponse.data.usage).toBeDefined();
        
        // Step 11: Get conversation history
        const messagesResponse = await apiClient.getMessages(conversationId, { include_usage: true });
        expect(messagesResponse.success).toBe(true);
        expect(messagesResponse.data.messages.length).toBeGreaterThan(0);
        
      } catch (error) {
        console.log('Assessment-based conversation creation skipped - endpoint may not be available');
      }
      
    }, 400000); // 6+ minute timeout for full flow
  });

  describe('Multi-User Concurrent Assessment Flow', () => {
    test('should handle multiple users submitting assessments concurrently', async () => {
      // Create second user
      const testUser2 = dataGenerator.generateUserData();
      const registerResponse2 = await apiClient.register(testUser2);
      const authToken2 = registerResponse2.data.token;
      
      // Create second API client and WebSocket
      const apiClient2 = new APIClient();
      const wsClient2 = new WebSocketClient();
      apiClient2.setAuthToken(authToken2);
      
      try {
        // Connect both WebSockets
        await wsClient.connect();
        await wsClient.authenticate(authToken);
        
        await wsClient2.connect();
        await wsClient2.authenticate(authToken2);
        
        expect(wsClient.isAuth()).toBe(true);
        expect(wsClient2.isAuth()).toBe(true);
        
        // Submit assessments for both users simultaneously
        const assessmentData1 = dataGenerator.generateAssessmentData();
        const assessmentData2 = dataGenerator.generateAssessmentData();
        
        const [submit1, submit2] = await Promise.all([
          apiClient.submitAssessment(assessmentData1),
          apiClient2.submitAssessment(assessmentData2)
        ]);
        
        expect(submit1.success).toBe(true);
        expect(submit2.success).toBe(true);
        
        // Each user should receive their own started notifications
        const [notification1, notification2] = await Promise.all([
          wsClient.waitForNotification('analysis-started', 30000),
          wsClient2.waitForNotification('analysis-started', 30000)
        ]);
        
        expect(notification1.data.jobId).toBe(submit1.data.jobId);
        expect(notification2.data.jobId).toBe(submit2.data.jobId);
        
        // Verify each user can only see their own results
        const results1 = await apiClient.getResults();
        const results2 = await apiClient2.getResults();
        
        expect(results1.success).toBe(true);
        expect(results2.success).toBe(true);
        
        // Results should be isolated per user
        expect(results1.data.results).toBeDefined();
        expect(results2.data.results).toBeDefined();
        
        // Cleanup second user
        wsClient2.disconnect();
        try {
          await apiClient2.deleteAccount();
        } catch (error) {
          // Ignore cleanup errors
        }
        
      } finally {
        if (wsClient2 && wsClient2.isConnected()) {
          wsClient2.disconnect();
        }
      }
    }, 120000); // 2 minute timeout
  });

  describe('Error Recovery and Resilience', () => {
    test('should recover from WebSocket disconnection during assessment', async () => {
      // Connect WebSocket
      await wsClient.connect();
      await wsClient.authenticate(authToken);
      
      // Submit assessment
      const assessmentData = dataGenerator.generateAssessmentData();
      const submitResponse = await apiClient.submitAssessment(assessmentData);
      const jobId = submitResponse.data.jobId;
      
      // Wait for started notification
      await wsClient.waitForNotification('analysis-started', 30000);
      
      // Simulate disconnection
      wsClient.disconnect();
      expect(wsClient.isConnected()).toBe(false);
      
      // Wait a bit to simulate network issue
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Reconnect and re-authenticate
      await wsClient.connect();
      await wsClient.authenticate(authToken);
      expect(wsClient.isAuth()).toBe(true);
      
      // Should still receive completion notification
      const completeNotification = await wsClient.waitForNotification('analysis-complete', 300000);
      expect(completeNotification.data.jobId).toBe(jobId);
      
      // Verify result is accessible via API
      const resultResponse = await apiClient.getResultById(completeNotification.data.resultId);
      expect(resultResponse.success).toBe(true);
      
    }, 400000); // 6+ minute timeout
  });

  describe('Complete User Profile Management Flow', () => {
    test('should complete profile management and school integration', async () => {
      // Step 1: Get initial profile
      const initialProfile = await apiClient.getProfile();
      expect(initialProfile.success).toBe(true);
      
      // Step 2: Update profile
      const profileUpdate = dataGenerator.generateProfileUpdateData();
      const updateResponse = await apiClient.updateProfile(profileUpdate);
      expect(updateResponse.success).toBe(true);
      expect(updateResponse.data.user.username).toBe(profileUpdate.username);
      
      // Step 3: Change password
      const passwordData = dataGenerator.generatePasswordChangeData();
      const passwordResponse = await apiClient.changePassword(passwordData);
      expect(passwordResponse.success).toBe(true);
      
      // Step 4: Check token balance
      const balanceResponse = await apiClient.getTokenBalance();
      expect(balanceResponse.success).toBe(true);
      expect(typeof balanceResponse.data.token_balance).toBe('number');
      
      // Step 5: Search and create school
      const schoolsResponse = await apiClient.getSchools({ search: 'SMA', limit: 5 });
      expect(schoolsResponse.success).toBe(true);
      
      const schoolData = dataGenerator.generateSchoolData();
      const createSchoolResponse = await apiClient.createSchool(schoolData);
      expect(createSchoolResponse.success).toBe(true);
      
      // Step 6: Update profile with school info
      const profileWithSchool = {
        ...profileUpdate,
        profile: {
          school_info: {
            type: "structured",
            school_id: createSchoolResponse.data.school.id
          }
        }
      };
      
      const finalUpdateResponse = await apiClient.updateProfile(profileWithSchool);
      expect(finalUpdateResponse.success).toBe(true);
      
    }, 60000); // 1 minute timeout
  });
});
