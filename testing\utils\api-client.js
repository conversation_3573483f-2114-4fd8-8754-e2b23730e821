const axios = require('axios');
const chalk = require('chalk');
require('dotenv').config();

class APIClient {
  constructor() {
    this.baseURL = process.env.API_BASE_URL || 'http://localhost:3000/api';
    this.timeout = parseInt(process.env.TIMEOUT_MS) || 30000;
    this.verbose = process.env.VERBOSE_LOGGING === 'true';
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        if (this.verbose) {
          console.log(chalk.blue(`→ ${config.method?.toUpperCase()} ${config.url}`));
          if (config.data) {
            console.log(chalk.gray('  Body:', JSON.stringify(config.data, null, 2)));
          }
        }
        return config;
      },
      (error) => {
        console.error(chalk.red('Request Error:', error.message));
        return Promise.reject(error);
      }
    );

    // Response interceptor for logging
    this.client.interceptors.response.use(
      (response) => {
        if (this.verbose) {
          console.log(chalk.green(`← ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`));
          if (response.data) {
            console.log(chalk.gray('  Response:', JSON.stringify(response.data, null, 2)));
          }
        }
        return response;
      },
      (error) => {
        if (this.verbose) {
          console.error(chalk.red(`← ${error.response?.status || 'ERROR'} ${error.config?.method?.toUpperCase()} ${error.config?.url}`));
          if (error.response?.data) {
            console.error(chalk.red('  Error:', JSON.stringify(error.response.data, null, 2)));
          }
        }
        return Promise.reject(error);
      }
    );
  }

  setAuthToken(token) {
    if (token) {
      this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete this.client.defaults.headers.common['Authorization'];
    }
  }

  // Auth endpoints
  async register(userData) {
    const response = await this.client.post('/auth/register', userData);
    return response.data;
  }

  async login(credentials) {
    const response = await this.client.post('/auth/login', credentials);
    return response.data;
  }

  async getProfile() {
    const response = await this.client.get('/auth/profile');
    return response.data;
  }

  async updateProfile(profileData) {
    const response = await this.client.put('/auth/profile', profileData);
    return response.data;
  }

  async logout() {
    const response = await this.client.post('/auth/logout');
    return response.data;
  }

  async changePassword(passwordData) {
    const response = await this.client.post('/auth/change-password', passwordData);
    return response.data;
  }

  async getTokenBalance() {
    const response = await this.client.get('/auth/token-balance');
    return response.data;
  }

  async getSchools(params = {}) {
    const response = await this.client.get('/auth/schools', { params });
    return response.data;
  }

  async createSchool(schoolData) {
    const response = await this.client.post('/auth/schools', schoolData);
    return response.data;
  }

  // Assessment endpoints
  async submitAssessment(assessmentData) {
    const response = await this.client.post('/assessment/submit', assessmentData);
    return response.data;
  }

  async getAssessmentStatus(jobId) {
    const response = await this.client.get(`/assessment/status/${jobId}`);
    return response.data;
  }

  // Archive endpoints
  async getResults(params = {}) {
    const response = await this.client.get('/archive/results', { params });
    return response.data;
  }

  async getResultById(resultId) {
    const response = await this.client.get(`/archive/results/${resultId}`);
    return response.data;
  }

  async deleteResult(resultId) {
    const response = await this.client.delete(`/archive/results/${resultId}`);
    return response.data;
  }

  async getJobs(params = {}) {
    const response = await this.client.get('/archive/jobs', { params });
    return response.data;
  }

  async getJobById(jobId) {
    const response = await this.client.get(`/archive/jobs/${jobId}`);
    return response.data;
  }

  async deleteJob(jobId) {
    const response = await this.client.delete(`/archive/jobs/${jobId}`);
    return response.data;
  }

  async getJobStats() {
    const response = await this.client.get('/archive/jobs/stats');
    return response.data;
  }

  async getStats(params = {}) {
    const response = await this.client.get('/archive/v1/stats', { params });
    return response.data;
  }

  // Chatbot endpoints
  async createConversation(conversationData) {
    const response = await this.client.post('/chatbot/conversations', conversationData);
    return response.data;
  }

  async sendMessage(conversationId, messageData) {
    const response = await this.client.post(`/chatbot/conversations/${conversationId}/messages`, messageData);
    return response.data;
  }

  async getConversations(params = {}) {
    const response = await this.client.get('/chatbot/conversations', { params });
    return response.data;
  }

  async getConversationById(conversationId, params = {}) {
    const response = await this.client.get(`/chatbot/conversations/${conversationId}`, { params });
    return response.data;
  }

  async updateConversation(conversationId, updateData) {
    const response = await this.client.put(`/chatbot/conversations/${conversationId}`, updateData);
    return response.data;
  }

  async deleteConversation(conversationId) {
    const response = await this.client.delete(`/chatbot/conversations/${conversationId}`);
    return response.data;
  }

  async getMessages(conversationId, params = {}) {
    const response = await this.client.get(`/chatbot/conversations/${conversationId}/messages`, { params });
    return response.data;
  }

  async regenerateMessage(conversationId, messageId) {
    const response = await this.client.post(`/chatbot/conversations/${conversationId}/messages/${messageId}/regenerate`);
    return response.data;
  }

  async checkAssessmentReady(userId) {
    const response = await this.client.get(`/chatbot/assessment-ready/${userId}`);
    return response.data;
  }

  async createConversationFromAssessment(assessmentData) {
    const response = await this.client.post('/chatbot/conversations/from-assessment', assessmentData);
    return response.data;
  }

  async getConversationSuggestions(conversationId) {
    const response = await this.client.get(`/chatbot/conversations/${conversationId}/suggestions`);
    return response.data;
  }

  async autoInitializeChatbot() {
    const response = await this.client.post('/chatbot/auto-initialize');
    return response.data;
  }

  // Notification endpoints
  async getNotificationHealth() {
    const response = await this.client.get('/notifications/health');
    return response.data;
  }

  // Admin endpoints
  async adminLogin(credentials) {
    const response = await this.client.post('/admin/login', credentials);
    return response.data;
  }

  async adminRegister(adminData) {
    const response = await this.client.post('/admin/register', adminData);
    return response.data;
  }

  async adminDeleteUser(userId) {
    const response = await this.client.delete(`/archive/admin/users/${userId}`);
    return response.data;
  }

  async adminUpdateTokenBalance(userId, balanceData) {
    const response = await this.client.put(`/archive/admin/users/${userId}/token-balance`, balanceData);
    return response.data;
  }

  // User deletion (for cleanup)
  async deleteAccount() {
    // Note: This endpoint might need to be implemented in the backend
    try {
      const response = await this.client.delete('/auth/account');
      return response.data;
    } catch (error) {
      if (error.response?.status === 404) {
        console.log(chalk.yellow('Delete account endpoint not implemented, skipping cleanup'));
        return { success: true, message: 'Cleanup skipped - endpoint not available' };
      }
      throw error;
    }
  }

  // Health check endpoints
  async healthCheck() {
    const response = await this.client.get('/health');
    return response.data;
  }

  async healthMetrics() {
    const response = await this.client.get('/health/metrics');
    return response.data;
  }

  async healthReady() {
    const response = await this.client.get('/health/ready');
    return response.data;
  }

  async healthLive() {
    const response = await this.client.get('/health/live');
    return response.data;
  }

  // Assessment health endpoints
  async assessmentHealth() {
    const response = await this.client.get('/assessment/health');
    return response.data;
  }

  async assessmentHealthReady() {
    const response = await this.client.get('/assessment/health/ready');
    return response.data;
  }

  async assessmentHealthLive() {
    const response = await this.client.get('/assessment/health/live');
    return response.data;
  }

  async assessmentHealthQueue() {
    const response = await this.client.get('/assessment/health/queue');
    return response.data;
  }

  // Response validation helpers
  validateSuccessResponse(response) {
    if (!response || typeof response !== 'object') {
      throw new Error('Response is not an object');
    }

    if (response.success !== true) {
      throw new Error(`Expected success: true, got: ${response.success}`);
    }

    if (!response.data && response.data !== null) {
      throw new Error('Response missing data field');
    }

    if (!response.timestamp && !response.message) {
      throw new Error('Response missing timestamp or message');
    }

    return true;
  }

  validateErrorResponse(response) {
    if (!response || typeof response !== 'object') {
      throw new Error('Response is not an object');
    }

    if (response.success !== false) {
      throw new Error(`Expected success: false, got: ${response.success}`);
    }

    if (!response.error || typeof response.error !== 'object') {
      throw new Error('Response missing error object');
    }

    if (!response.error.code || !response.error.message) {
      throw new Error('Error object missing code or message');
    }

    if (!response.error.timestamp) {
      throw new Error('Error object missing timestamp');
    }

    return true;
  }

  validatePaginationResponse(response) {
    this.validateSuccessResponse(response);

    if (!response.data.pagination) {
      throw new Error('Response missing pagination object');
    }

    const pagination = response.data.pagination;
    const requiredFields = ['page', 'limit', 'total', 'totalPages', 'hasNext', 'hasPrev'];

    for (const field of requiredFields) {
      if (pagination[field] === undefined) {
        throw new Error(`Pagination missing field: ${field}`);
      }
    }

    return true;
  }

  validateSecurityHeaders(response) {
    const headers = response.headers || {};
    const requiredHeaders = ['x-gateway', 'x-gateway-version', 'x-request-id'];

    for (const header of requiredHeaders) {
      if (!headers[header]) {
        console.warn(`Missing security header: ${header}`);
      }
    }

    return true;
  }
}

module.exports = APIClient;
