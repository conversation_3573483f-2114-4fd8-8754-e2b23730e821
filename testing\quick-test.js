#!/usr/bin/env node

/**
 * Quick Test Runner
 * 
 * Simple script to run quick tests and health checks
 */

const APIClient = require('./utils/api-client');
const WebSocketClient = require('./utils/websocket-client');
const TestDataGenerator = require('./utils/test-data');
const TestLogger = require('./utils/test-logger');
const chalk = require('chalk');
require('dotenv').config();

class QuickTest {
  constructor() {
    this.logger = new TestLogger('Quick Test');
    this.apiClient = new APIClient();
    this.dataGenerator = new TestDataGenerator();
  }

  async runHealthChecks() {
    this.logger.header('Health Checks');
    
    const checks = [
      {
        name: 'API Gateway',
        url: `${process.env.API_BASE_URL?.replace('/api', '') || 'http://localhost:3000'}/api/health`,
        test: () => this.apiClient.healthCheck()
      },
      {
        name: 'Auth Service',
        test: async () => {
          const testUser = this.dataGenerator.generateUserData();
          const response = await this.apiClient.register(testUser);
          if (response.success) {
            // Cleanup
            this.apiClient.setAuthToken(response.data.token);
            try {
              await this.apiClient.deleteAccount();
            } catch (e) {
              // Ignore cleanup errors
            }
          }
          return response;
        }
      },
      {
        name: 'WebSocket Connection',
        test: async () => {
          const wsClient = new WebSocketClient();
          try {
            await wsClient.connect();
            wsClient.disconnect();
            return { success: true, message: 'WebSocket connection successful' };
          } catch (error) {
            throw error;
          }
        }
      }
    ];

    const results = [];
    
    for (const check of checks) {
      try {
        this.logger.info(`Testing ${check.name}...`);
        const startTime = Date.now();
        
        const result = await check.test();
        const duration = Date.now() - startTime;
        
        if (result.success !== false) {
          this.logger.success(`${check.name} - OK (${duration}ms)`);
          results.push({ name: check.name, status: 'OK', duration });
        } else {
          this.logger.error(`${check.name} - FAILED`);
          results.push({ name: check.name, status: 'FAILED', duration });
        }
      } catch (error) {
        this.logger.error(`${check.name} - ERROR: ${error.message}`);
        results.push({ name: check.name, status: 'ERROR', error: error.message });
      }
    }

    this.logger.table('Health Check Results', results);
    
    const failedChecks = results.filter(r => r.status !== 'OK');
    if (failedChecks.length > 0) {
      this.logger.error(`${failedChecks.length} health checks failed`);
      return false;
    } else {
      this.logger.success('All health checks passed');
      return true;
    }
  }

  async runQuickUserTest() {
    this.logger.header('Quick User Flow Test');
    
    let token = null;
    
    try {
      // Register user
      this.logger.step('Register test user');
      const userData = this.dataGenerator.generateUserData();
      const registerResponse = await this.apiClient.register(userData);
      
      if (!registerResponse.success) {
        throw new Error('Registration failed');
      }
      
      token = registerResponse.data.token;
      this.apiClient.setAuthToken(token);
      this.logger.success(`User registered: ${userData.email}`);

      // Get profile
      this.logger.step('Get user profile');
      const profileResponse = await this.apiClient.getProfile();
      
      if (!profileResponse.success) {
        throw new Error('Get profile failed');
      }
      
      this.logger.success('Profile retrieved successfully');

      // Submit assessment
      this.logger.step('Submit assessment');
      const assessmentData = this.dataGenerator.generateAssessmentData();
      const assessmentResponse = await this.apiClient.submitAssessment(assessmentData);
      
      if (!assessmentResponse.success) {
        throw new Error('Assessment submission failed');
      }
      
      this.logger.success(`Assessment submitted: ${assessmentResponse.data.jobId}`);

      // Test chatbot
      this.logger.step('Test chatbot');
      const conversationData = this.dataGenerator.generateChatbotConversationData();
      const conversationResponse = await this.apiClient.createConversation(conversationData);
      
      if (!conversationResponse.success) {
        throw new Error('Conversation creation failed');
      }
      
      this.logger.success('Chatbot test completed');

      // Cleanup
      this.logger.step('Cleanup');
      try {
        await this.apiClient.deleteAccount();
        this.logger.success('Test account deleted');
      } catch (cleanupError) {
        this.logger.warning('Account cleanup failed (endpoint may not be implemented)');
      }

      this.logger.success('Quick user flow test completed successfully');
      return true;

    } catch (error) {
      this.logger.error('Quick user flow test failed', error);
      return false;
    }
  }

  async runQuickWebSocketTest() {
    this.logger.header('Quick WebSocket Test');
    
    const wsClient = new WebSocketClient();
    let token = null;
    
    try {
      // Register user for WebSocket test
      this.logger.step('Register user for WebSocket test');
      const userData = this.dataGenerator.generateUserData();
      const registerResponse = await this.apiClient.register(userData);
      
      if (!registerResponse.success) {
        throw new Error('Registration failed');
      }
      
      token = registerResponse.data.token;
      this.apiClient.setAuthToken(token);

      // Connect WebSocket
      this.logger.step('Connect WebSocket');
      await wsClient.connect();
      this.logger.success('WebSocket connected');

      // Authenticate WebSocket
      this.logger.step('Authenticate WebSocket');
      await wsClient.authenticate(token);
      this.logger.success('WebSocket authenticated');

      // Test notification (submit assessment and wait for started notification)
      this.logger.step('Test notification');
      const assessmentData = this.dataGenerator.generateAssessmentData();
      await this.apiClient.submitAssessment(assessmentData);
      
      try {
        const notification = await wsClient.waitForNotification('analysis-started', 30000);
        this.logger.success('Received WebSocket notification');
      } catch (notificationError) {
        this.logger.warning('WebSocket notification timeout (this may be normal)');
      }

      // Cleanup
      wsClient.disconnect();
      this.logger.success('WebSocket disconnected');
      
      try {
        await this.apiClient.deleteAccount();
        this.logger.success('Test account deleted');
      } catch (cleanupError) {
        this.logger.warning('Account cleanup failed');
      }

      this.logger.success('Quick WebSocket test completed successfully');
      return true;

    } catch (error) {
      this.logger.error('Quick WebSocket test failed', error);
      
      if (wsClient.isConnected()) {
        wsClient.disconnect();
      }
      
      return false;
    }
  }

  async runAll() {
    this.logger.header('Quick Test Suite');
    
    const tests = [
      { name: 'Health Checks', test: () => this.runHealthChecks() },
      { name: 'User Flow', test: () => this.runQuickUserTest() },
      { name: 'WebSocket', test: () => this.runQuickWebSocketTest() }
    ];

    const results = [];
    
    for (const test of tests) {
      try {
        this.logger.separator();
        const success = await test.test();
        results.push({ name: test.name, status: success ? 'PASSED' : 'FAILED' });
      } catch (error) {
        this.logger.error(`${test.name} test failed`, error);
        results.push({ name: test.name, status: 'ERROR', error: error.message });
      }
    }

    this.logger.separator();
    this.logger.table('Quick Test Results', results);
    
    const failedTests = results.filter(r => r.status !== 'PASSED');
    if (failedTests.length > 0) {
      this.logger.error(`${failedTests.length} tests failed`);
      process.exit(1);
    } else {
      this.logger.success('All quick tests passed!');
      process.exit(0);
    }
  }

  async showConfiguration() {
    this.logger.header('Configuration');
    
    const config = {
      'API Base URL': process.env.API_BASE_URL || 'http://localhost:3000/api',
      'WebSocket URL': process.env.WEBSOCKET_URL || 'http://localhost:3000',
      'Timeout (ms)': process.env.TIMEOUT_MS || '30000',
      'Assessment Timeout (ms)': process.env.ASSESSMENT_TIMEOUT_MS || '300000',
      'Test Email Domain': process.env.TEST_EMAIL_DOMAIN || 'test.atma.local',
      'Cleanup After Test': process.env.CLEANUP_AFTER_TEST || 'true',
      'Verbose Logging': process.env.VERBOSE_LOGGING || 'true'
    };

    this.logger.table('Current Configuration', Object.entries(config).map(([key, value]) => ({
      Setting: key,
      Value: value
    })));
  }
}

// Command line interface
async function main() {
  const command = process.argv[2] || 'all';
  const quickTest = new QuickTest();

  switch (command) {
    case 'health':
      console.log(chalk.blue('🏥 Running health checks...'));
      const healthOk = await quickTest.runHealthChecks();
      process.exit(healthOk ? 0 : 1);
      break;

    case 'user':
      console.log(chalk.blue('👤 Running user flow test...'));
      const userOk = await quickTest.runQuickUserTest();
      process.exit(userOk ? 0 : 1);
      break;

    case 'websocket':
    case 'ws':
      console.log(chalk.blue('🔌 Running WebSocket test...'));
      const wsOk = await quickTest.runQuickWebSocketTest();
      process.exit(wsOk ? 0 : 1);
      break;

    case 'config':
      await quickTest.showConfiguration();
      break;

    case 'all':
      console.log(chalk.blue('🚀 Running all quick tests...'));
      await quickTest.runAll();
      break;

    case 'help':
    case '-h':
    case '--help':
      console.log(chalk.bold('Quick Test Runner'));
      console.log('');
      console.log('Usage: node quick-test.js [command]');
      console.log('');
      console.log('Commands:');
      console.log('  all        Run all quick tests (default)');
      console.log('  health     Run health checks only');
      console.log('  user       Run user flow test only');
      console.log('  websocket  Run WebSocket test only');
      console.log('  config     Show current configuration');
      console.log('  help       Show this help message');
      console.log('');
      console.log('Examples:');
      console.log('  node quick-test.js');
      console.log('  node quick-test.js health');
      console.log('  node quick-test.js user');
      console.log('  node quick-test.js websocket');
      break;

    default:
      console.error(chalk.red(`Unknown command: ${command}`));
      console.log('Use "node quick-test.js help" for usage information');
      process.exit(1);
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error(chalk.red('Quick test failed:'), error.message);
    process.exit(1);
  });
}

module.exports = QuickTest;
