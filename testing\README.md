# ATMA Backend Testing Suite

Comprehensive testing suite untuk ATMA Backend API yang mencakup testing 2 user dengan skenario lengkap: register → login → connect websocket → update profile → submit assessment → tunggu notif dari websocket → lihat profile_persona → test chatbot → delete account.

## 🚀 Quick Start

### Prerequisites

1. **Node.js** (v16 atau lebih baru)
2. **ATMA Backend Services** berjalan di:
   - API Gateway: `http://localhost:3000`
   - Notification Service: `http://localhost:3005` (atau via Gateway)

### Installation

```bash
# Install dependencies
npm install

# Copy dan edit environment variables
cp .env.example .env
```

### Environment Configuration

Edit file `.env` sesuai keb<PERSON>uhan:

```env
# ATMA Backend Testing Configuration
API_BASE_URL=http://localhost:3000/api
WEBSOCKET_URL=http://localhost:3000
TIMEOUT_MS=30000
ASSESSMENT_TIMEOUT_MS=300000

# Test Configuration
TEST_EMAIL_DOMAIN=test.atma.local
CLEANUP_AFTER_TEST=true
VERBOSE_LOGGING=true

# Assessment Test Data
DEFAULT_ASSESSMENT_NAME=AI-Driven Talent Mapping
```

## 🧪 Running Tests

### Full Scenario Test (2 Users)

Test lengkap untuk 2 user dengan email random yang menjalankan seluruh flow:

```bash
npm run test:full-scenario
```

### Individual Tests

```bash
# Test user flow (register → login → assessment → chatbot)
npm run test:user-flow

# Test WebSocket connection dan notifications
npm run test:websocket

# Run all tests with Jest
npm test

# Watch mode untuk development
npm run test:watch

# Coverage report
npm run test:coverage

# NEW: Test comprehensive API coverage (all missing endpoints)
npm test tests/comprehensive-api.test.js

# NEW: Test complete integration flows
npm test tests/integration-flow.test.js

# NEW: Test health endpoints compliance
npm run test:health

# NEW: Test WebSocket notification validation
npm run test:websocket-validation

# NEW: Run all compliance tests
npm run test:all-compliance
```

## 📋 Test Scenarios

### Full Scenario Test

Testing 2 user secara parallel dengan skenario:

1. **User 1 - Tech Enthusiast**
   - Email random: `user-{uuid}@test.atma.local`
   - Profile: Tech-oriented dengan RIASEC tinggi di Investigative
   - Assessment: Fokus pada teknologi dan analisis

2. **User 2 - Creative Professional**
   - Email random: `user-{uuid}@test.atma.local`
   - Profile: Creative-oriented dengan RIASEC tinggi di Artistic
   - Assessment: Fokus pada kreativitas dan social skills

### Test Flow untuk Setiap User

1. ✅ **Register** dengan email random
2. ✅ **Login** dengan credentials
3. ✅ **Connect WebSocket** ke notification service
4. ✅ **Authenticate WebSocket** dengan JWT token
5. ✅ **Update Profile** dengan data baru
6. ✅ **Submit Assessment** dengan data RIASEC, OCEAN, VIA-IS
7. ✅ **Wait for WebSocket Notifications**:
   - `analysis-started` notification
   - `analysis-complete` notification
8. ✅ **Retrieve Assessment Results** (persona profile)
9. ✅ **Test Chatbot**:
   - Create conversation
   - Send message
   - Receive AI response
10. ✅ **Delete Account** (cleanup)

## 📁 Test Files Structure

### Core Test Files
- `tests/api.test.js` - Basic API endpoint testing (updated with new endpoints)
- `tests/websocket.test.js` - WebSocket connection and notification testing (enhanced)
- `tests/comprehensive-api.test.js` - **NEW** - Extended API coverage with all missing endpoints
- `tests/integration-flow.test.js` - **NEW** - Complete user journey integration tests
- `tests/health-endpoints.test.js` - **NEW** - Health endpoints compliance testing
- `tests/websocket-validation.test.js` - **NEW** - WebSocket notification data structure validation
- `tests/user-flow-test.js` - User journey testing
- `tests/full-scenario-test.js` - Complete scenario testing

### New Test Coverage Added
- **Complete API Specification Coverage**: All endpoints from API Gateway documentation
- **Response Format Validation**: Validates responses match specification format
- **Admin Functionality**: Tests for admin endpoints (when available)
- **Assessment-Chatbot Integration**: Tests for complete assessment to chatbot flow
- **Multi-User Scenarios**: Tests for concurrent user operations
- **Error Recovery**: Tests for WebSocket reconnection and error handling
- **Schools Management**: Tests for school creation and search functionality
- **Extended Archive Operations**: Tests for jobs, statistics, and advanced filtering

## 🔧 Test Components

### API Client (`utils/api-client.js`) - **ENHANCED**

HTTP client untuk semua API endpoints dengan:
- Automatic request/response logging
- JWT token management
- Error handling
- **NEW**: Support untuk semua ATMA endpoints sesuai spesifikasi
- **NEW**: Response format validation helpers
- **NEW**: Admin functionality support
- **NEW**: Extended archive operations (jobs, statistics)
- **NEW**: Complete chatbot integration endpoints
- **NEW**: Schools management endpoints
- **NEW**: Assessment-chatbot integration endpoints

### WebSocket Client (`utils/websocket-client.js`) - **ENHANCED**

WebSocket client untuk real-time notifications dengan:
- Connection management
- **IMPROVED**: Better error handling and reconnection logic
- **NEW**: Enhanced notification validation
- **NEW**: Support for multiple concurrent connections testing
- **NEW**: Improved authentication timeout handling
- Authentication handling
- Notification waiting dan filtering
- Reconnection support

### Test Data Generator (`utils/test-data.js`)

Generator untuk test data dengan:
- Random email generation
- Assessment data dengan realistic scores
- User profile data
- Chatbot conversation data

### Test Logger (`utils/test-logger.js`)

Comprehensive logging dengan:
- Colored output
- Step tracking
- Timing measurements
- JSON formatting
- Test summaries

## 📊 Test Output

### Success Example

```
🚀 ATMA Backend Full Scenario Test - 2 Users
═══════════════════════════════════════════════════════════════════════════════

🚀 Testing Scenario: User 1 - Tech Enthusiast
═══════════════════════════════════════════════════════════════════════════════
📋 [User 1] Step 1: Register user with random email
✅ [User 1] User registered: <EMAIL>
📋 [User 1] Step 2: Login user
✅ [User 1] User logged in successfully
📋 [User 1] Step 3: Connect to WebSocket
✅ [User 1] WebSocket connected
📋 [User 1] Step 4: Authenticate WebSocket
✅ [User 1] WebSocket authenticated
📋 [User 1] Step 5: Update user profile
✅ [User 1] Profile updated successfully
📋 [User 1] Step 6: Submit assessment
✅ [User 1] Assessment submitted with job ID: job_550e8400-e29b-41d4-a716-************
📋 [User 1] Step 7: Wait for assessment completion notification
✅ [User 1] Received analysis-started notification
✅ [User 1] Received analysis-complete notification
⏱️  Assessment processing time: 45000ms
📋 [User 1] Step 8: Retrieve assessment results
✅ [User 1] Assessment results retrieved - Archetype: The Innovator
📋 [User 1] Step 9: Test chatbot functionality
✅ [User 1] Conversation created: conv_550e8400-e29b-41d4-a716-************
✅ [User 1] Chatbot interaction completed
📋 [User 1] Step 10: Cleanup - Delete account
✅ [User 1] Account deleted successfully
✅ [User 1] WebSocket disconnected
═══════════════════════════════════════════════════════════════════════════════
✨ Test completed in 47500ms

📋 Test Summary
──────────────────────────────────────────────────────
✅ Passed: 2/2
❌ Failed: 0/2

📊 Success Rate: 100%
```

### Error Handling

Test suite menangani berbagai error scenarios:
- Network timeouts
- Authentication failures
- Invalid data submissions
- WebSocket connection issues
- API rate limiting

## 🔍 Debugging

### Verbose Logging

Set `VERBOSE_LOGGING=true` di `.env` untuk detailed logs:

```bash
VERBOSE_LOGGING=true npm run test:full-scenario
```

### Individual Component Testing

```bash
# Test hanya WebSocket functionality
node tests/websocket-test.js

# Test hanya user flow tanpa WebSocket
node tests/user-flow-test.js
```

### Manual Testing

```javascript
const APIClient = require('./utils/api-client');
const client = new APIClient();

// Test individual endpoints
client.register({ email: '<EMAIL>', password: 'pass', username: 'test' })
  .then(response => console.log(response))
  .catch(error => console.error(error));
```

## 📝 Configuration Options

### Timeouts

- `TIMEOUT_MS`: General API timeout (default: 30000ms)
- `ASSESSMENT_TIMEOUT_MS`: Assessment processing timeout (default: 300000ms)

### Cleanup

- `CLEANUP_AFTER_TEST`: Delete test accounts after completion (default: true)

### Email Domain

- `TEST_EMAIL_DOMAIN`: Domain untuk test emails (default: test.atma.local)

## 🚨 Troubleshooting

### Common Issues

1. **Connection Refused**
   ```
   Error: connect ECONNREFUSED 127.0.0.1:3000
   ```
   - Pastikan ATMA Backend services berjalan
   - Check `API_BASE_URL` di `.env`

2. **WebSocket Authentication Timeout**
   ```
   Error: Authentication timeout
   ```
   - Check JWT token validity
   - Pastikan notification service berjalan

3. **Assessment Processing Timeout**
   ```
   Error: Assessment did not complete within timeout
   ```
   - Increase `ASSESSMENT_TIMEOUT_MS`
   - Check assessment service status

### Debug Steps

1. Check service health:
   ```bash
   curl http://localhost:3000/api/health
   ```

2. Enable verbose logging:
   ```bash
   VERBOSE_LOGGING=true npm run test:full-scenario
   ```

3. Test individual components:
   ```bash
   npm run test:user-flow
   npm run test:websocket
   ```

## 🆕 Recent Updates - Full API Specification Compliance

### What Was Fixed
The testing implementation has been **significantly enhanced** to match the complete API Gateway and WebSocket specifications:

#### Missing Endpoints Added:
- **Authentication**: `/auth/change-password`, `/auth/token-balance`, `/auth/schools`
- **Archive**: `/archive/jobs/*`, `/archive/v1/stats`, job management endpoints
- **Chatbot**: Assessment integration endpoints, conversation management
- **Admin**: Admin login, user management, token balance management
- **Notifications**: Health check endpoint
- **Health Endpoints**: `/health/*`, `/assessment/health/*` endpoints

#### New Test Coverage:
- **Response Format Validation**: All responses now validated against specification
- **Pagination Testing**: Proper pagination format validation
- **Error Response Testing**: Error format validation according to spec
- **Multi-User Scenarios**: Concurrent user testing
- **Integration Flows**: Complete user journey testing
- **Admin Functionality**: Admin endpoint testing (when available)
- **WebSocket Resilience**: Reconnection and error recovery testing
- **Health Endpoints**: Complete health check endpoint testing
- **WebSocket Data Validation**: Notification structure validation according to manual

#### Enhanced Features:
- **Complete API Client**: All 50+ endpoints now supported
- **Better Error Handling**: Graceful handling of missing endpoints
- **Response Validation**: Built-in validation helpers
- **Comprehensive Test Data**: Generators for all new endpoint types
- **WebSocket Notification Validation**: Real-time validation of notification data structure
- **Health Monitoring**: Comprehensive health endpoint testing

### Compliance Status
✅ **API Gateway Specification**: 100% endpoint coverage including health endpoints
✅ **WebSocket Manual**: Full implementation with enhanced testing and data validation
✅ **Response Formats**: All formats validated against specification
✅ **Error Handling**: Proper error response validation
✅ **Integration Testing**: Complete user journey coverage
✅ **Health Monitoring**: Complete health endpoint compliance
✅ **Notification Validation**: WebSocket data structure validation according to manual

## 📚 API Documentation Reference

Test suite mengikuti spesifikasi dari:
- `api-gateway/api_external.md` - API endpoints dan request/response format
- `notification-service/WEBSOCKET_MANUAL.md` - WebSocket connection dan events

## 🤝 Contributing

1. Tambah test cases di folder `tests/`
2. Update utilities di folder `utils/`
3. Follow existing logging dan error handling patterns
4. Test dengan `npm test` sebelum commit

## 📄 License

MIT License - lihat file LICENSE untuk detail.
