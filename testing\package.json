{"name": "atma-backend-testing", "version": "1.0.0", "description": "Testing suite for ATMA Backend API", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:user-flow": "node tests/user-flow-test.js", "test:websocket": "node tests/websocket-test.js", "test:full-scenario": "node tests/full-scenario-test.js", "test:health": "jest tests/health-endpoints.test.js", "test:websocket-validation": "jest tests/websocket-validation.test.js", "test:comprehensive": "jest tests/comprehensive-api.test.js", "test:integration": "jest tests/integration-flow.test.js", "test:all-compliance": "jest tests/health-endpoints.test.js tests/websocket-validation.test.js tests/comprehensive-api.test.js", "quick-test": "node quick-test.js", "quick-test:health": "node quick-test.js health", "quick-test:user": "node quick-test.js user", "quick-test:ws": "node quick-test.js websocket", "example": "node examples/manual-test-example.js", "setup": "npm install && cp .env.example .env", "clean": "rm -rf node_modules coverage .env"}, "dependencies": {"axios": "^1.6.0", "socket.io-client": "^4.7.2", "uuid": "^9.0.1", "chalk": "^4.1.2", "dotenv": "^16.3.1"}, "devDependencies": {"jest": "^29.7.0", "@types/jest": "^29.5.8"}, "keywords": ["testing", "api", "websocket", "atma"], "author": "ATMA Team", "license": "MIT"}