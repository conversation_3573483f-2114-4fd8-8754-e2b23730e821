const { v4: uuidv4 } = require('uuid');

class TestDataGenerator {
  constructor() {
    this.emailDomain = process.env.TEST_EMAIL_DOMAIN || 'example.com';
  }

  generateRandomEmail() {
    const randomId = uuidv4().replace(/-/g, '').substring(0, 8);
    return `user${randomId}@${this.emailDomain}`;
  }

  generateUserData() {
    const randomId = uuidv4().replace(/-/g, '').substring(0, 8);
    return {
      email: this.generateRandomEmail(),
      password: 'TestPassword123!',
      username: `testuser${randomId}`
    };
  }

  generateProfileUpdateData() {
    const randomId = uuidv4().replace(/-/g, '').substring(0, 8);
    return {
      username: `updateduser${randomId}`,
      full_name: `Test User ${randomId}`,
      email: this.generateRandomEmail()
    };
  }

  generateAssessmentData() {
    return {
      assessmentName: process.env.DEFAULT_ASSESSMENT_NAME || "AI-Driven Talent Mapping",
      riasec: {
        realistic: this.randomScore(),
        investigative: this.randomScore(),
        artistic: this.randomScore(),
        social: this.randomScore(),
        enterprising: this.randomScore(),
        conventional: this.randomScore()
      },
      ocean: {
        openness: this.randomScore(),
        conscientiousness: this.randomScore(),
        extraversion: this.randomScore(),
        agreeableness: this.randomScore(),
        neuroticism: this.randomScore()
      },
      viaIs: {
        creativity: this.randomScore(),
        curiosity: this.randomScore(),
        judgment: this.randomScore(),
        loveOfLearning: this.randomScore(),
        perspective: this.randomScore(),
        bravery: this.randomScore(),
        perseverance: this.randomScore(),
        honesty: this.randomScore(),
        zest: this.randomScore(),
        love: this.randomScore(),
        kindness: this.randomScore(),
        socialIntelligence: this.randomScore(),
        teamwork: this.randomScore(),
        fairness: this.randomScore(),
        leadership: this.randomScore(),
        forgiveness: this.randomScore(),
        humility: this.randomScore(),
        prudence: this.randomScore(),
        selfRegulation: this.randomScore(),
        appreciationOfBeauty: this.randomScore(),
        gratitude: this.randomScore(),
        hope: this.randomScore(),
        humor: this.randomScore(),
        spirituality: this.randomScore()
      }
    };
  }

  generateChatbotConversationData() {
    return {
      title: "Career Guidance Chat",
      context_type: "career_guidance",
      context_data: {},
      metadata: {}
    };
  }

  generateChatMessage() {
    const messages = [
      "Hello, I need career guidance based on my assessment results.",
      "What career paths would suit my personality type?",
      "How can I develop my identified strengths?",
      "What skills should I focus on improving?",
      "Tell me about careers in technology.",
      "How can I improve my leadership skills?"
    ];

    return {
      content: messages[Math.floor(Math.random() * messages.length)],
      content_type: "text",
      parent_message_id: null
    };
  }

  generatePasswordChangeData() {
    return {
      currentPassword: 'TestPassword123!',
      newPassword: 'NewTestPassword123!'
    };
  }

  generateSchoolData() {
    const randomId = uuidv4().replace(/-/g, '').substring(0, 8);
    return {
      name: `SMA Test ${randomId}`,
      address: `Jl. Test No. ${Math.floor(Math.random() * 100)}`,
      city: "Jakarta",
      province: "DKI Jakarta"
    };
  }

  generateAdminData() {
    const randomId = uuidv4().replace(/-/g, '').substring(0, 8);
    return {
      username: `admin${randomId}`,
      email: `admin${randomId}@${this.emailDomain}`,
      password: 'AdminPassword123!',
      full_name: `Admin User ${randomId}`,
      user_type: "admin"
    };
  }

  generateTokenBalanceData() {
    return {
      amount: Math.floor(Math.random() * 100) + 10,
      operation: Math.random() > 0.5 ? 'add' : 'subtract'
    };
  }

  generateConversationFromAssessmentData() {
    return {
      assessment_id: `result_${uuidv4()}`,
      title: "Career Guidance Based on Assessment",
      auto_start_message: true
    };
  }

  randomScore(min = 40, max = 100) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  // Generate test scenarios
  generateTestScenarios() {
    return [
      {
        name: "User 1 - Tech Enthusiast",
        userData: {
          email: this.generateRandomEmail(),
          password: 'TechUser123!',
          username: `techuser${uuidv4().replace(/-/g, '').substring(0, 8)}`
        },
        profileUpdate: {
          username: `techenthusiast${uuidv4().replace(/-/g, '').substring(0, 8)}`,
          full_name: "Tech Enthusiast User",
          email: this.generateRandomEmail()
        },
        assessmentData: {
          ...this.generateAssessmentData(),
          riasec: {
            realistic: 85,
            investigative: 92,
            artistic: 65,
            social: 55,
            enterprising: 75,
            conventional: 60
          }
        }
      },
      {
        name: "User 2 - Creative Professional",
        userData: {
          email: this.generateRandomEmail(),
          password: 'CreativeUser123!',
          username: `creativeuser${uuidv4().replace(/-/g, '').substring(0, 8)}`
        },
        profileUpdate: {
          username: `creativepro${uuidv4().replace(/-/g, '').substring(0, 8)}`,
          full_name: "Creative Professional User",
          email: this.generateRandomEmail()
        },
        assessmentData: {
          ...this.generateAssessmentData(),
          riasec: {
            realistic: 45,
            investigative: 70,
            artistic: 95,
            social: 80,
            enterprising: 65,
            conventional: 40
          }
        }
      }
    ];
  }
}

module.exports = TestDataGenerator;
